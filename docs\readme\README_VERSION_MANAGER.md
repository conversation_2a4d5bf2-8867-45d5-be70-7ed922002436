# FSJ04832 版本管理工具 (图形界面版)

## 🎯 工具简介

FSJ04832 版本管理工具是一个可视化的版本管理和打包工具，让您可以通过友好的图形界面轻松管理版本号和执行打包操作，无需记忆复杂的命令行参数。

## ✨ 主要特性

### 🖥️ 图形化界面
- **直观操作**: 点击式操作，无需命令行
- **实时反馈**: 构建过程实时显示
- **错误处理**: 友好的错误提示和处理

### 🔢 智能版本管理
- **四段式版本号**: 主版本.次版本.补丁版本.构建号
- **自动增加**: 支持不同类型的版本号增加
- **预览功能**: 构建前预览版本变化

### 🎨 优化的用户体验
- **大字体显示**: 微软雅黑字体，12px 大小
- **清晰界面**: 高对比度颜色，易于阅读
- **现代设计**: 圆角按钮，渐变效果

### 🔄 完整的构建流程
- **版本更新**: 自动更新版本号和构建时间
- **文件同步**: 自动更新窗口标题和可执行文件名
- **打包构建**: 集成 PyInstaller 打包功能

## 🚀 快速开始

### 方法一：双击启动（推荐）
```
双击 "启动版本管理工具.bat" 文件
```

### 方法二：Python 命令
```bash
python version_manager.py
```

### 方法三：测试字体效果
```bash
python test_font_improvements.py
```

## 📋 界面说明

### 1. 当前版本信息
显示项目的完整版本信息：
- 当前版本号
- 应用程序名称
- 窗口标题预览
- 可执行文件名
- 构建日期和类型

### 2. 版本号管理
选择版本号增加类型：

| 类型 | 用途 | 示例变化 | 适用场景 |
|------|------|----------|----------|
| **构建号** | 日常开发 | `1.0.0.0` → `1.0.0.1` | 代码调试、小修复 |
| **补丁版本** | Bug修复 | `1.0.0.5` → `1.0.1.0` | 重要修复、安全更新 |
| **次版本** | 新功能 | `1.0.5.0` → `1.1.0.0` | 功能添加、API扩展 |
| **主版本** | 重大更新 | `1.5.0.0` → `2.0.0.0` | 架构变更、重大重构 |

### 3. 构建选项
- **Spec文件**: 指定 PyInstaller 配置文件
- **浏览功能**: 可视化选择文件路径

### 4. 操作控制
- **预览变化**: 查看版本更新效果
- **开始构建**: 执行版本更新和打包
- **停止构建**: 中断构建过程

### 5. 输出监控
- **进度显示**: 实时构建进度条
- **详细日志**: 完整的构建输出信息
- **深色主题**: 专业的终端风格显示

## 🎨 字体优化特性

### 视觉改进
- ✅ **微软雅黑字体**: 清晰的中文显示
- ✅ **12px 大字体**: 比默认字体大 20%
- ✅ **高对比度**: 蓝色主题，易于识别
- ✅ **背景色区分**: 重要信息有背景色
- ✅ **圆角设计**: 现代化的界面风格

### 控件优化
- ✅ **大按钮**: 10px 内边距，易于点击
- ✅ **大选择框**: 16x16px 单选框和复选框
- ✅ **清晰边框**: 2px 边框，明确区域划分
- ✅ **悬停效果**: 鼠标悬停时的视觉反馈

## 📊 使用流程

### 标准开发流程
```
1. 启动工具 → 2. 选择版本类型 → 3. 预览变化 → 4. 开始构建 → 5. 查看结果
```

### 具体操作步骤

#### 日常开发构建
1. 选择 "构建号 (Build)"
2. 点击 "预览版本变化"
3. 确认无误后点击 "开始构建"
4. 等待构建完成

#### 发布版本
1. 根据更新类型选择相应版本
2. 预览版本变化
3. 开始构建
4. 验证构建结果

## 📁 输出结果

构建完成后将获得：

### 版本化的可执行文件
```
dist/FSJConfigTool1.0.1.exe
```

### 同步更新的信息
- `version.json` - 版本配置文件
- `build.spec` - PyInstaller 配置
- 应用程序窗口标题
- 关于对话框信息

## 🔧 高级功能

### 自定义配置
- **Spec文件路径**: 支持自定义 PyInstaller 配置
- **版本号策略**: 灵活的版本号管理规则
- **构建选项**: 可选择是否增加版本号

### 错误处理
- **文件检查**: 自动检查必要文件是否存在
- **构建监控**: 实时监控构建状态
- **异常恢复**: 构建失败时的恢复机制

## 🧪 测试验证

### 功能测试
```bash
# 运行完整测试
python test_version_gui.py

# 测试字体改进
python test_font_improvements.py

# 测试版本集成
python test_suite/ui/test_version_integration.py
```

### 手动验证
- [ ] 界面字体是否清晰
- [ ] 版本信息是否正确
- [ ] 构建功能是否正常
- [ ] 文件输出是否正确

## ⚠️ 注意事项

### 系统要求
- **Python**: 3.6 或更高版本
- **PyQt5**: GUI 框架依赖
- **PyInstaller**: 打包工具依赖

### 使用建议
- **备份重要文件**: 构建前备份重要配置
- **版本控制**: 建议使用 Git 管理版本历史
- **测试验证**: 构建后测试应用程序功能

## 🐛 故障排除

### 常见问题

#### 启动失败
```
问题: 双击 bat 文件无反应
解决: 检查 Python 是否正确安装
```

#### 字体显示问题
```
问题: 字体仍然不够清晰
解决: 检查系统是否安装微软雅黑字体
```

#### 构建失败
```
问题: 构建过程中出现错误
解决: 查看输出区域的详细错误信息
```

## 📈 版本历史

- **v1.0.0**: 基础命令行版本
- **v1.1.0**: 图形界面版本
- **v1.2.0**: 字体优化版本（当前）

## 🎯 未来计划

- [ ] 支持更多打包选项
- [ ] 添加版本历史管理
- [ ] 集成自动测试功能
- [ ] 支持多语言界面

## 📞 技术支持

如有问题或建议，请：
1. 查看相关文档
2. 运行测试脚本
3. 联系开发团队

---

**🎉 享受更清晰、更便捷的版本管理体验！**
