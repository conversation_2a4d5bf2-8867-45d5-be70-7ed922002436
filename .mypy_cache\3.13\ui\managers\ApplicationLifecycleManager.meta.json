{"data_mtime": 1752742474, "dep_lines": [9, 10, 11, 183, 8, 172, 212, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 20, 10, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["PyQt5.QtCore", "utils.Log", "utils.CursorUtils", "PyQt5.QtWidgets", "traceback", "sys", "os", "builtins", "PyQt5", "PyQt5.QtGui", "PyQt5.sip", "_collections_abc", "_frozen_importlib", "abc", "types", "typing", "utils"], "hash": "c8d364d2d38b878b47f75cca56123be914050107", "id": "ui.managers.ApplicationLifecycleManager", "ignore_all": false, "interface_hash": "2556a0951ca0a2d9267df1845320670d801d5316", "mtime": 1751600285, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\managers\\ApplicationLifecycleManager.py", "plugin_data": null, "size": 10624, "suppressed": [], "version_id": "1.15.0"}