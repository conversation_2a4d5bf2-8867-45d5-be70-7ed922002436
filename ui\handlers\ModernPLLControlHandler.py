# -*- coding: utf-8 -*-

"""
ModernPLLControlHandler - PLL控制处理器
提供PLL相关的控制和管理功能
"""

import logging
from PyQt5.QtCore import QObject, pyqtSignal
from PyQt5.QtWidgets import QWidget

from .ModernBaseHandler import ModernBaseHandler

logger = logging.getLogger(__name__)

class ModernPLLControlHandler(ModernBaseHandler):
    """现代化PLL控制处理器"""
    
    # 信号定义
    pll_frequency_changed = pyqtSignal(str, float)  # PLL频率变化信号
    pll_mode_changed = pyqtSignal(str, str)         # PLL模式变化信号
    pll_lock_status_changed = pyqtSignal(str, bool) # PLL锁定状态变化信号
    
    def __init__(self, parent=None, register_manager=None):
        """初始化PLL控制处理器
        
        Args:
            parent: 父对象
            register_manager: 寄存器管理器
        """
        super().__init__(parent, register_manager)
        
        # PLL控制状态
        self.pll_configs = {
            'PLL1': {
                'frequency': 0.0,
                'mode': 'DISABLED',
                'locked': False,
                'reference_freq': 0.0,
                'feedback_divider': 1,
                'reference_divider': 1
            },
            'PLL2': {
                'frequency': 0.0,
                'mode': 'DISABLED', 
                'locked': False,
                'reference_freq': 0.0,
                'feedback_divider': 1,
                'reference_divider': 1
            }
        }
        
        # 初始化PLL控制
        self._initialize_pll_control()
        
        logger.info("ModernPLLControlHandler初始化完成")
        
    def _initialize_pll_control(self):
        """初始化PLL控制"""
        try:
            # 从寄存器读取PLL配置
            self._load_pll_configs_from_registers()
            
            # 设置默认值
            self._set_default_pll_values()
            
            logger.debug("PLL控制初始化完成")
            
        except Exception as e:
            logger.error(f"PLL控制初始化失败: {str(e)}")
            
    def _load_pll_configs_from_registers(self):
        """从寄存器加载PLL配置"""
        if not self.register_manager:
            return
            
        try:
            # PLL1相关寄存器
            pll1_registers = [
                "0x10", "0x11", "0x12", "0x13", "0x14", "0x15"
            ]
            
            # PLL2相关寄存器
            pll2_registers = [
                "0x20", "0x21", "0x22", "0x23", "0x24", "0x25"
            ]
            
            # 读取PLL1配置
            for addr in pll1_registers:
                try:
                    value = self.register_manager.get_register_value(addr)
                    self._parse_pll_register('PLL1', addr, value)
                except Exception as e:
                    logger.debug(f"读取PLL1寄存器{addr}失败: {str(e)}")
                    
            # 读取PLL2配置
            for addr in pll2_registers:
                try:
                    value = self.register_manager.get_register_value(addr)
                    self._parse_pll_register('PLL2', addr, value)
                except Exception as e:
                    logger.debug(f"读取PLL2寄存器{addr}失败: {str(e)}")
                    
        except Exception as e:
            logger.error(f"从寄存器加载PLL配置失败: {str(e)}")
            
    def _parse_pll_register(self, pll_name, addr, value):
        """解析PLL寄存器值
        
        Args:
            pll_name: PLL名称 ('PLL1' 或 'PLL2')
            addr: 寄存器地址
            value: 寄存器值
        """
        try:
            if pll_name not in self.pll_configs:
                return
                
            # 根据地址解析不同的PLL参数
            # 这里是示例实现，实际需要根据FSJ04832的寄存器定义
            if addr.endswith('0'):  # 控制寄存器
                # 解析PLL使能、模式等
                enabled = bool(value & 0x01)
                mode = 'ENABLED' if enabled else 'DISABLED'
                self.pll_configs[pll_name]['mode'] = mode
                
            elif addr.endswith('1'):  # 分频器寄存器
                # 解析反馈分频器
                feedback_div = (value & 0xFF00) >> 8
                reference_div = value & 0x00FF
                self.pll_configs[pll_name]['feedback_divider'] = feedback_div
                self.pll_configs[pll_name]['reference_divider'] = reference_div
                
            # 计算频率
            self._calculate_pll_frequency(pll_name)
            
        except Exception as e:
            logger.error(f"解析PLL寄存器失败 {pll_name} {addr}: {str(e)}")
            
    def _calculate_pll_frequency(self, pll_name):
        """计算PLL频率
        
        Args:
            pll_name: PLL名称
        """
        try:
            config = self.pll_configs[pll_name]
            
            # 简化的PLL频率计算
            # 实际公式需要根据FSJ04832规格书
            ref_freq = config['reference_freq']
            fb_div = config['feedback_divider']
            ref_div = config['reference_divider']
            
            if ref_div > 0:
                pll_freq = (ref_freq * fb_div) / ref_div
                config['frequency'] = pll_freq
                
                # 发送频率变化信号
                self.pll_frequency_changed.emit(pll_name, pll_freq)
                
        except Exception as e:
            logger.error(f"计算PLL频率失败 {pll_name}: {str(e)}")
            
    def _set_default_pll_values(self):
        """设置默认PLL值"""
        try:
            # 设置默认参考频率（通常来自外部晶振）
            default_ref_freq = 25.0e6  # 25MHz
            
            for pll_name in self.pll_configs:
                self.pll_configs[pll_name]['reference_freq'] = default_ref_freq
                self._calculate_pll_frequency(pll_name)
                
        except Exception as e:
            logger.error(f"设置默认PLL值失败: {str(e)}")
            
    def get_pll_frequency(self, pll_name):
        """获取PLL频率
        
        Args:
            pll_name: PLL名称
            
        Returns:
            float: PLL频率（Hz）
        """
        if pll_name in self.pll_configs:
            return self.pll_configs[pll_name]['frequency']
        return 0.0
        
    def set_pll_frequency(self, pll_name, frequency):
        """设置PLL频率
        
        Args:
            pll_name: PLL名称
            frequency: 目标频率（Hz）
            
        Returns:
            bool: 设置是否成功
        """
        try:
            if pll_name not in self.pll_configs:
                return False
                
            # 计算所需的分频器值
            ref_freq = self.pll_configs[pll_name]['reference_freq']
            if ref_freq <= 0:
                return False
                
            # 简化的分频器计算
            fb_div = int(frequency / ref_freq)
            if fb_div < 1:
                fb_div = 1
            elif fb_div > 255:
                fb_div = 255
                
            # 更新配置
            self.pll_configs[pll_name]['feedback_divider'] = fb_div
            self._calculate_pll_frequency(pll_name)
            
            # 写入寄存器（如果有寄存器管理器）
            if self.register_manager:
                self._write_pll_config_to_registers(pll_name)
                
            return True
            
        except Exception as e:
            logger.error(f"设置PLL频率失败 {pll_name}: {str(e)}")
            return False
            
    def _write_pll_config_to_registers(self, pll_name):
        """将PLL配置写入寄存器
        
        Args:
            pll_name: PLL名称
        """
        try:
            config = self.pll_configs[pll_name]
            
            # 根据PLL名称确定寄存器地址
            if pll_name == 'PLL1':
                base_addr = 0x10
            elif pll_name == 'PLL2':
                base_addr = 0x20
            else:
                return
                
            # 写入分频器配置
            fb_div = config['feedback_divider']
            ref_div = config['reference_divider']
            div_value = (fb_div << 8) | ref_div
            
            div_addr = f"0x{base_addr + 1:02X}"
            self.register_manager.set_register_value(div_addr, div_value)
            
            logger.debug(f"PLL配置已写入寄存器 {pll_name}: {div_addr}=0x{div_value:04X}")
            
        except Exception as e:
            logger.error(f"写入PLL配置到寄存器失败 {pll_name}: {str(e)}")
            
    def get_pll_mode(self, pll_name):
        """获取PLL模式
        
        Args:
            pll_name: PLL名称
            
        Returns:
            str: PLL模式
        """
        if pll_name in self.pll_configs:
            return self.pll_configs[pll_name]['mode']
        return 'UNKNOWN'
        
    def set_pll_mode(self, pll_name, mode):
        """设置PLL模式
        
        Args:
            pll_name: PLL名称
            mode: PLL模式 ('ENABLED', 'DISABLED', 'BYPASS')
            
        Returns:
            bool: 设置是否成功
        """
        try:
            if pll_name not in self.pll_configs:
                return False
                
            valid_modes = ['ENABLED', 'DISABLED', 'BYPASS']
            if mode not in valid_modes:
                return False
                
            # 更新模式
            old_mode = self.pll_configs[pll_name]['mode']
            self.pll_configs[pll_name]['mode'] = mode
            
            # 发送模式变化信号
            if old_mode != mode:
                self.pll_mode_changed.emit(pll_name, mode)
                
            return True
            
        except Exception as e:
            logger.error(f"设置PLL模式失败 {pll_name}: {str(e)}")
            return False
            
    def get_pll_lock_status(self, pll_name):
        """获取PLL锁定状态
        
        Args:
            pll_name: PLL名称
            
        Returns:
            bool: PLL是否锁定
        """
        if pll_name in self.pll_configs:
            return self.pll_configs[pll_name]['locked']
        return False
        
    def get_all_pll_status(self):
        """获取所有PLL状态
        
        Returns:
            dict: 所有PLL的状态信息
        """
        return {
            pll_name: {
                'frequency': config['frequency'],
                'mode': config['mode'],
                'locked': config['locked']
            }
            for pll_name, config in self.pll_configs.items()
        }
        
    def reset_pll(self, pll_name):
        """重置PLL
        
        Args:
            pll_name: PLL名称
            
        Returns:
            bool: 重置是否成功
        """
        try:
            if pll_name not in self.pll_configs:
                return False
                
            # 重置PLL配置到默认值
            self.pll_configs[pll_name].update({
                'frequency': 0.0,
                'mode': 'DISABLED',
                'locked': False,
                'feedback_divider': 1,
                'reference_divider': 1
            })
            
            # 重新计算频率
            self._calculate_pll_frequency(pll_name)
            
            logger.info(f"PLL {pll_name} 已重置")
            return True
            
        except Exception as e:
            logger.error(f"重置PLL失败 {pll_name}: {str(e)}")
            return False
