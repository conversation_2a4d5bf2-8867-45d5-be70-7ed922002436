#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文档整合工具
根据重复分析结果，自动整合重复的文档
"""

import os
import shutil
from pathlib import Path
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DocumentConsolidator:
    """文档整合器"""
    
    def __init__(self, docs_dir="docs"):
        self.docs_dir = Path(docs_dir)
        self.backup_dir = self.docs_dir / f"backup_before_consolidation_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 定义整合计划（基于分析结果）
        self.consolidation_plan = {
            "InternalVCOFreq计算_综合说明.md": {
                "main_file": "features/修正InternalVCOFreq计算逻辑的完整方案.md",
                "merge_files": [
                    "features/InternalVCOFreq计算问题修复说明.md",
                    "features/修正InternalVCOFreq计算公式.md"
                ],
                "target_dir": "features",
                "description": "InternalVCOFreq计算相关的所有文档"
            },
            "SYSREF缓存机制_综合说明.md": {
                "main_file": "features/添加PLL2NDivider缓存机制的完整方案.md",
                "merge_files": [
                    "features/实现SYSREF缓存机制解决PLL2Cin显示问题.md",
                    "features/SYSREF频率计算问题诊断和修复.md"
                ],
                "target_dir": "features",
                "description": "SYSREF缓存机制相关的所有文档"
            },
            "PLL2计算_综合说明.md": {
                "main_file": "features/调试PLL2Cin显示0问题的解决方案.md",
                "merge_files": [
                    "features/PLL2简化计算方法说明.md",
                    "features/PLL2Cin显示0问题的完整解决方案.md"
                ],
                "target_dir": "features",
                "description": "PLL2计算相关的所有文档"
            },
            "VCODistFreq计算_综合说明.md": {
                "main_file": "features/修复VCODistFreq与InternalVCOFreq同步问题.md",
                "merge_files": [
                    "features/VCODistFreq同步功能说明.md",
                    "features/Fin0模式VCODistFreq计算逻辑修改说明.md"
                ],
                "target_dir": "features",
                "description": "VCODistFreq计算相关的所有文档"
            },
            "文档整理_综合报告.md": {
                "main_file": "DOCUMENT_ORGANIZATION_REPORT.md",
                "merge_files": [
                    "MD_ORGANIZATION_REPORT.md"
                ],
                "target_dir": ".",
                "description": "文档整理相关的报告"
            }
        }
        
    def create_backup(self):
        """创建备份"""
        try:
            self.backup_dir.mkdir(exist_ok=True)
            
            # 备份所有要修改的文件
            backed_up_files = []
            
            for new_filename, plan in self.consolidation_plan.items():
                # 备份主文件
                main_file_path = self.docs_dir / plan["main_file"]
                if main_file_path.exists():
                    backup_path = self.backup_dir / main_file_path.name
                    shutil.copy2(main_file_path, backup_path)
                    backed_up_files.append(main_file_path.name)
                    
                # 备份要合并的文件
                for merge_file in plan["merge_files"]:
                    merge_file_path = self.docs_dir / merge_file
                    if merge_file_path.exists():
                        backup_path = self.backup_dir / merge_file_path.name
                        # 如果文件名重复，添加序号
                        counter = 1
                        while backup_path.exists():
                            stem = merge_file_path.stem
                            suffix = merge_file_path.suffix
                            backup_path = self.backup_dir / f"{stem}_{counter}{suffix}"
                            counter += 1
                        shutil.copy2(merge_file_path, backup_path)
                        backed_up_files.append(merge_file_path.name)
                        
            logger.info(f"备份完成: {len(backed_up_files)}个文件 -> {self.backup_dir}")
            return True
            
        except Exception as e:
            logger.error(f"创建备份失败: {str(e)}")
            return False
            
    def merge_documents(self, new_filename, plan):
        """合并文档"""
        try:
            main_file_path = self.docs_dir / plan["main_file"]
            target_dir = self.docs_dir / plan["target_dir"]
            new_file_path = target_dir / new_filename
            
            # 读取主文件内容
            main_content = ""
            if main_file_path.exists():
                with open(main_file_path, 'r', encoding='utf-8') as f:
                    main_content = f.read()
                    
            # 创建新的综合文档
            merged_content = self._create_merged_content(new_filename, plan, main_content)
            
            # 写入新文件
            with open(new_file_path, 'w', encoding='utf-8') as f:
                f.write(merged_content)
                
            logger.info(f"创建综合文档: {new_file_path}")
            return new_file_path
            
        except Exception as e:
            logger.error(f"合并文档失败 {new_filename}: {str(e)}")
            return None
            
    def _create_merged_content(self, new_filename, plan, main_content):
        """创建合并后的文档内容"""
        title = new_filename.replace('.md', '').replace('_', ' ')
        
        merged_content = f"""# {title}

## 📚 文档整合说明

本文档整合了以下相关文档的内容：

### 主要文档
- **{Path(plan["main_file"]).name}** - 作为主要内容基础

### 合并文档
"""
        
        for merge_file in plan["merge_files"]:
            merged_content += f"- **{Path(merge_file).name}** - 补充相关内容\n"
            
        merged_content += f"""
### 整合目的
{plan["description"]}，避免内容重复，提高文档维护效率。

### 整合时间
{datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}

---

## 📖 主要内容

"""
        
        # 添加主文件内容（去掉原标题）
        if main_content:
            lines = main_content.split('\n')
            # 跳过第一个一级标题
            content_start = 0
            for i, line in enumerate(lines):
                if line.strip().startswith('# '):
                    content_start = i + 1
                    break
                    
            main_body = '\n'.join(lines[content_start:])
            merged_content += main_body
            
        # 添加合并文件的内容
        for merge_file in plan["merge_files"]:
            merge_file_path = self.docs_dir / merge_file
            if merge_file_path.exists():
                try:
                    with open(merge_file_path, 'r', encoding='utf-8') as f:
                        merge_content = f.read()
                        
                    merged_content += f"\n\n---\n\n## 📄 来自 {Path(merge_file).name}\n\n"
                    
                    # 添加合并文件内容（去掉原标题，调整标题级别）
                    lines = merge_content.split('\n')
                    for line in lines:
                        if line.strip().startswith('# '):
                            # 一级标题改为三级标题
                            merged_content += f"### {line[2:]}\n"
                        elif line.strip().startswith('## '):
                            # 二级标题改为四级标题
                            merged_content += f"#### {line[3:]}\n"
                        elif line.strip().startswith('### '):
                            # 三级标题改为五级标题
                            merged_content += f"##### {line[4:]}\n"
                        else:
                            merged_content += f"{line}\n"
                            
                except Exception as e:
                    logger.error(f"读取合并文件失败 {merge_file}: {str(e)}")
                    merged_content += f"\n\n⚠️ 无法读取文件内容: {merge_file}\n\n"
                    
        # 添加文档尾部
        merged_content += f"""

---

## 📋 整合信息

- **整合时间**: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}
- **原始文档数**: {len(plan["merge_files"]) + 1}个
- **备份位置**: `{self.backup_dir.name}/`
- **整合工具**: 自动文档整合工具 v1.0

### 原始文档列表

1. **主文档**: {plan["main_file"]}
"""
        
        for i, merge_file in enumerate(plan["merge_files"], 2):
            merged_content += f"{i}. **合并文档**: {merge_file}\n"
            
        merged_content += """
### 注意事项

- 原始文档已备份，如需恢复可从备份目录获取
- 本文档包含了所有原始文档的完整内容
- 如发现内容缺失或错误，请检查备份文件

---

*本文档由自动整合工具生成，如有问题请联系维护人员*
"""
        
        return merged_content
        
    def remove_original_files(self, plan):
        """删除原始文件"""
        removed_files = []
        
        try:
            # 删除主文件
            main_file_path = self.docs_dir / plan["main_file"]
            if main_file_path.exists():
                main_file_path.unlink()
                removed_files.append(plan["main_file"])
                
            # 删除合并文件
            for merge_file in plan["merge_files"]:
                merge_file_path = self.docs_dir / merge_file
                if merge_file_path.exists():
                    merge_file_path.unlink()
                    removed_files.append(merge_file)
                    
            logger.info(f"删除原始文件: {len(removed_files)}个")
            return removed_files
            
        except Exception as e:
            logger.error(f"删除原始文件失败: {str(e)}")
            return []
            
    def update_category_readme(self, target_dir, new_filename, plan):
        """更新分类README"""
        try:
            readme_path = self.docs_dir / target_dir / "README.md"
            
            if readme_path.exists():
                with open(readme_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
                # 移除原始文档的链接
                lines = content.split('\n')
                new_lines = []
                
                for line in lines:
                    should_remove = False
                    
                    # 检查是否是要删除的文档链接
                    main_filename = Path(plan["main_file"]).name
                    if f"[{main_filename}]" in line or f"({main_filename})" in line:
                        should_remove = True
                        
                    for merge_file in plan["merge_files"]:
                        merge_filename = Path(merge_file).name
                        if f"[{merge_filename}]" in line or f"({merge_filename})" in line:
                            should_remove = True
                            break
                            
                    if not should_remove:
                        new_lines.append(line)
                        
                # 添加新的综合文档链接
                if "## 文档列表" in content:
                    for i, line in enumerate(new_lines):
                        if line.strip() == "## 文档列表":
                            new_lines.insert(i + 2, f"- [{new_filename}](./{new_filename}) - {plan['description']}")
                            break
                            
                # 写回文件
                with open(readme_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(new_lines))
                    
                logger.info(f"更新分类README: {readme_path}")
                
        except Exception as e:
            logger.error(f"更新分类README失败: {str(e)}")
            
    def consolidate_all(self):
        """执行所有文档整合"""
        logger.info("开始文档整合...")
        
        # 创建备份
        if not self.create_backup():
            logger.error("备份失败，停止整合")
            return False
            
        consolidated_files = []
        removed_files = []
        
        # 执行整合
        for new_filename, plan in self.consolidation_plan.items():
            logger.info(f"整合文档组: {new_filename}")
            
            # 合并文档
            new_file_path = self.merge_documents(new_filename, plan)
            if new_file_path:
                consolidated_files.append(new_filename)
                
                # 删除原始文件
                removed = self.remove_original_files(plan)
                removed_files.extend(removed)
                
                # 更新分类README
                self.update_category_readme(plan["target_dir"], new_filename, plan)
                
        # 输出结果
        print("\n" + "="*60)
        print("📚 文档整合完成报告")
        print("="*60)
        
        print(f"\n✅ 成功整合:")
        for filename in consolidated_files:
            print(f"   📄 {filename}")
            
        print(f"\n🗑️  删除原始文档:")
        for filename in removed_files:
            print(f"   📄 {filename}")
            
        print(f"\n📊 整合统计:")
        print(f"   原始文档数: {len(removed_files)}")
        print(f"   整合后文档数: {len(consolidated_files)}")
        print(f"   减少文档数: {len(removed_files) - len(consolidated_files)}")
        print(f"   减少比例: {(len(removed_files) - len(consolidated_files)) / len(removed_files) * 100:.1f}%")
        
        print(f"\n💾 备份信息:")
        print(f"   备份位置: {self.backup_dir}")
        print(f"   备份文件数: {len(list(self.backup_dir.glob('*')))}")
        
        print(f"\n🎯 整合效果:")
        print(f"   ✅ 消除内容重复")
        print(f"   ✅ 提高查找效率")
        print(f"   ✅ 降低维护复杂度")
        print(f"   ✅ 统一文档结构")
        
        return True

def main():
    """主函数"""
    try:
        consolidator = DocumentConsolidator()
        success = consolidator.consolidate_all()
        
        if success:
            print("\n🎉 文档整合成功完成!")
            print("\n💡 建议:")
            print("   1. 检查整合后的文档内容是否完整")
            print("   2. 更新主文档索引")
            print("   3. 如有问题可从备份目录恢复")
        else:
            print("\n❌ 文档整合失败!")
            
        return success
        
    except Exception as e:
        logger.error(f"文档整合失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
