#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
文档重复分析工具
分析docs目录下的文档，找出重复内容和可以整合的文档
"""

import os
import re
from pathlib import Path
from collections import defaultdict
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DocumentDuplicateAnalyzer:
    """文档重复分析器"""
    
    def __init__(self, docs_dir="docs"):
        self.docs_dir = Path(docs_dir)
        
        # 定义重复模式
        self.duplicate_patterns = {
            "PLL2相关": {
                "keywords": ["PLL2", "PLL2Cin", "PLL2NDivider", "PLL2PFD"],
                "files": []
            },
            "VCODistFreq相关": {
                "keywords": ["VCODistFreq", "VCO分布频率", "VCO Dist"],
                "files": []
            },
            "SYSREF相关": {
                "keywords": ["SYSREF", "系统参考", "SysRef"],
                "files": []
            },
            "缓存机制": {
                "keywords": ["缓存", "cache", "缓存机制"],
                "files": []
            },
            "修复问题": {
                "keywords": ["修复", "修正", "解决方案", "问题"],
                "files": []
            },
            "计算方法": {
                "keywords": ["计算", "公式", "算法"],
                "files": []
            },
            "同步功能": {
                "keywords": ["同步", "sync", "实时"],
                "files": []
            },
            "InternalVCOFreq": {
                "keywords": ["InternalVCOFreq", "内部VCO频率"],
                "files": []
            }
        }
        
        # 明确的重复文档组
        self.known_duplicates = {
            "文档整理报告": [
                "DOCUMENT_ORGANIZATION_REPORT.md",
                "MD_ORGANIZATION_REPORT.md"
            ],
            "PLL2计算相关": [
                "features/PLL2简化计算方法说明.md",
                "features/PLL2Cin显示0问题的完整解决方案.md",
                "features/调试PLL2Cin显示0问题的解决方案.md"
            ],
            "VCODistFreq计算": [
                "features/VCODistFreq同步功能说明.md",
                "features/修复VCODistFreq与InternalVCOFreq同步问题.md",
                "features/Fin0模式VCODistFreq计算逻辑修改说明.md"
            ],
            "SYSREF缓存机制": [
                "features/实现SYSREF缓存机制解决PLL2Cin显示问题.md",
                "features/添加PLL2NDivider缓存机制的完整方案.md",
                "features/SYSREF频率计算问题诊断和修复.md"
            ],
            "InternalVCOFreq计算": [
                "features/InternalVCOFreq计算问题修复说明.md",
                "features/修正InternalVCOFreq计算公式.md",
                "features/修正InternalVCOFreq计算逻辑的完整方案.md"
            ]
        }
        
    def scan_all_documents(self):
        """扫描所有文档文件"""
        documents = []
        
        for file_path in self.docs_dir.rglob("*.md"):
            if file_path.is_file():
                documents.append(file_path)
                
        return documents
        
    def analyze_document_content(self, file_path):
        """分析文档内容"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 提取关键信息
            info = {
                'path': file_path,
                'size': len(content),
                'lines': len(content.split('\n')),
                'title': self._extract_title(content),
                'keywords': self._extract_keywords(content),
                'sections': self._extract_sections(content)
            }
            
            return info
            
        except Exception as e:
            logger.error(f"分析文档失败 {file_path}: {str(e)}")
            return None
            
    def _extract_title(self, content):
        """提取文档标题"""
        lines = content.split('\n')
        for line in lines:
            if line.strip().startswith('# '):
                return line.strip()[2:]
        return "无标题"
        
    def _extract_keywords(self, content):
        """提取关键词"""
        keywords = set()
        content_lower = content.lower()
        
        for category, pattern_info in self.duplicate_patterns.items():
            for keyword in pattern_info["keywords"]:
                if keyword.lower() in content_lower:
                    keywords.add(keyword)
                    
        return list(keywords)
        
    def _extract_sections(self, content):
        """提取章节标题"""
        sections = []
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if line.startswith('## '):
                sections.append(line[3:])
            elif line.startswith('### '):
                sections.append(line[4:])
                
        return sections
        
    def find_duplicate_content(self, documents_info):
        """查找重复内容"""
        # 按关键词分组
        for doc_info in documents_info:
            if not doc_info:
                continue
                
            for keyword in doc_info['keywords']:
                for category, pattern_info in self.duplicate_patterns.items():
                    if keyword in pattern_info["keywords"]:
                        pattern_info["files"].append(doc_info)
                        
        # 查找重复文档
        duplicates = {}
        
        for category, pattern_info in self.duplicate_patterns.items():
            if len(pattern_info["files"]) > 1:
                duplicates[category] = pattern_info["files"]
                
        return duplicates
        
    def analyze_known_duplicates(self):
        """分析已知的重复文档组"""
        analysis_results = {}
        
        for group_name, file_list in self.known_duplicates.items():
            group_analysis = {
                'files': [],
                'total_size': 0,
                'overlap_analysis': [],
                'merge_suggestion': None
            }
            
            # 分析每个文件
            for filename in file_list:
                file_path = self.docs_dir / filename
                if file_path.exists():
                    doc_info = self.analyze_document_content(file_path)
                    if doc_info:
                        group_analysis['files'].append(doc_info)
                        group_analysis['total_size'] += doc_info['size']
                        
            # 分析重叠度
            if len(group_analysis['files']) > 1:
                group_analysis['overlap_analysis'] = self._analyze_content_overlap(group_analysis['files'])
                group_analysis['merge_suggestion'] = self._generate_merge_suggestion(group_name, group_analysis['files'])
                
            analysis_results[group_name] = group_analysis
            
        return analysis_results
        
    def _analyze_content_overlap(self, files):
        """分析内容重叠度"""
        overlaps = []
        
        for i, file1 in enumerate(files):
            for j, file2 in enumerate(files[i+1:], i+1):
                # 简单的关键词重叠分析
                keywords1 = set(file1['keywords'])
                keywords2 = set(file2['keywords'])
                
                common_keywords = keywords1.intersection(keywords2)
                overlap_ratio = len(common_keywords) / max(len(keywords1), len(keywords2), 1)
                
                overlaps.append({
                    'file1': file1['path'].name,
                    'file2': file2['path'].name,
                    'common_keywords': list(common_keywords),
                    'overlap_ratio': overlap_ratio
                })
                
        return overlaps
        
    def _generate_merge_suggestion(self, group_name, files):
        """生成合并建议"""
        if not files:
            return None
            
        # 找出最大的文件作为主文件
        main_file = max(files, key=lambda x: x['size'])
        other_files = [f for f in files if f != main_file]
        
        suggestion = {
            'action': 'merge',
            'main_file': main_file['path'].name,
            'merge_files': [f['path'].name for f in other_files],
            'new_filename': f"{group_name.replace(' ', '_')}_综合说明.md",
            'estimated_size': sum(f['size'] for f in files),
            'sections_to_merge': []
        }
        
        # 分析需要合并的章节
        all_sections = set()
        for file_info in files:
            all_sections.update(file_info['sections'])
            
        suggestion['sections_to_merge'] = list(all_sections)
        
        return suggestion
        
    def generate_consolidation_report(self):
        """生成整合报告"""
        logger.info("开始分析文档重复...")
        
        # 扫描所有文档
        documents = self.scan_all_documents()
        logger.info(f"发现文档: {len(documents)}个")
        
        # 分析文档内容
        documents_info = []
        for doc_path in documents:
            info = self.analyze_document_content(doc_path)
            if info:
                documents_info.append(info)
                
        # 查找重复内容
        duplicates = self.find_duplicate_content(documents_info)
        
        # 分析已知重复组
        known_analysis = self.analyze_known_duplicates()
        
        return {
            'total_documents': len(documents),
            'analyzed_documents': len(documents_info),
            'duplicate_categories': duplicates,
            'known_duplicates_analysis': known_analysis,
            'consolidation_suggestions': self._generate_consolidation_suggestions(known_analysis)
        }
        
    def _generate_consolidation_suggestions(self, known_analysis):
        """生成整合建议"""
        suggestions = []
        
        for group_name, analysis in known_analysis.items():
            if len(analysis['files']) > 1:
                suggestion = analysis['merge_suggestion']
                if suggestion:
                    suggestions.append({
                        'group': group_name,
                        'priority': self._calculate_priority(analysis),
                        'action': suggestion['action'],
                        'details': suggestion
                    })
                    
        # 按优先级排序
        suggestions.sort(key=lambda x: x['priority'], reverse=True)
        
        return suggestions
        
    def _calculate_priority(self, analysis):
        """计算整合优先级"""
        priority = 0
        
        # 文件数量越多，优先级越高
        priority += len(analysis['files']) * 10
        
        # 总大小越大，优先级越高
        priority += analysis['total_size'] / 1000
        
        # 重叠度越高，优先级越高
        if analysis['overlap_analysis']:
            avg_overlap = sum(o['overlap_ratio'] for o in analysis['overlap_analysis']) / len(analysis['overlap_analysis'])
            priority += avg_overlap * 20
            
        return priority

def main():
    """主函数"""
    try:
        analyzer = DocumentDuplicateAnalyzer()
        report = analyzer.generate_consolidation_report()
        
        # 输出报告
        print("\n" + "="*60)
        print("📚 文档重复分析和整合建议报告")
        print("="*60)
        
        print(f"\n📊 基本统计:")
        print(f"   总文档数: {report['total_documents']}")
        print(f"   已分析文档数: {report['analyzed_documents']}")
        
        print(f"\n🔍 发现的重复类别:")
        for category, files in report['duplicate_categories'].items():
            if files:
                print(f"   📁 {category}: {len(files)}个文档")
                for file_info in files[:3]:  # 只显示前3个
                    print(f"      - {file_info['path'].name}")
                if len(files) > 3:
                    print(f"      ... 还有{len(files)-3}个文档")
                    
        print(f"\n🎯 整合建议 (按优先级排序):")
        for i, suggestion in enumerate(report['consolidation_suggestions'], 1):
            print(f"\n   {i}. 【{suggestion['group']}】(优先级: {suggestion['priority']:.1f})")
            details = suggestion['details']
            print(f"      主文件: {details['main_file']}")
            print(f"      合并文件: {', '.join(details['merge_files'])}")
            print(f"      建议新文件名: {details['new_filename']}")
            print(f"      预估大小: {details['estimated_size']/1000:.1f}KB")
            
        print(f"\n💡 整合收益:")
        total_files_before = sum(len(analysis['files']) for analysis in report['known_duplicates_analysis'].values())
        total_files_after = len(report['consolidation_suggestions'])
        if total_files_before > 0:
            reduction = (total_files_before - total_files_after) / total_files_before * 100
            print(f"   文档数量减少: {total_files_before} → {total_files_after} (-{reduction:.1f}%)")
            print(f"   维护复杂度降低: 显著")
            print(f"   查找效率提升: 显著")
            
        return True
        
    except Exception as e:
        logger.error(f"分析失败: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 文档重复分析完成!")
    else:
        print("\n❌ 文档重复分析失败!")
