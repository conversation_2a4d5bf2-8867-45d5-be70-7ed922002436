{"data_mtime": 1750155114, "dep_lines": [1, 3, 4, 5, 320, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["PyQt5.QtWidgets", "PyQt5.QtCore", "PyQt5.QtGui", "utils.Log", "traceback", "builtins", "PyQt5", "PyQt5.sip", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "typing", "utils"], "hash": "6542a0bfd426ff2ec6bc00e53e8634666c6b3267", "id": "ui.handlers.RegisterTableHandler", "ignore_all": false, "interface_hash": "f60038360a8d6153c307b74e7bf83fcfb77d3ff9", "mtime": 1751518745, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\RegisterTableHandler.py", "plugin_data": null, "size": 16949, "suppressed": [], "version_id": "1.15.0"}