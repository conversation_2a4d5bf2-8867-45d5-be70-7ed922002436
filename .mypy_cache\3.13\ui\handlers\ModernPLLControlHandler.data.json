{".class": "MypyFile", "_fullname": "ui.handlers.ModernPLLControlHandler", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ModernBaseHandler": {".class": "SymbolTableNode", "cross_ref": "ui.handlers.ModernBaseHandler.ModernBaseHandler", "kind": "Gdef"}, "ModernPLLControlHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["ui.handlers.ModernBaseHandler.ModernBaseHandler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler", "name": "ModernPLLControlHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "ui.handlers.ModernPLLControlHandler", "mro": ["ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler", "ui.handlers.ModernBaseHandler.ModernBaseHandler", "PyQt5.QtWidgets.QWidget", "PyQt5.QtCore.QObject", "PyQt5.sip.wrapper", "PyQt5.sip.simplewrapper", "PyQt5.QtGui.QPaintDevice", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "parent", "register_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler.__init__", "name": "__init__", "type": null}}, "_calculate_pll_frequency": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pll_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler._calculate_pll_frequency", "name": "_calculate_pll_frequency", "type": null}}, "_initialize_pll_control": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler._initialize_pll_control", "name": "_initialize_pll_control", "type": null}}, "_load_pll_configs_from_registers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler._load_pll_configs_from_registers", "name": "_load_pll_configs_from_registers", "type": null}}, "_parse_pll_register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "pll_name", "addr", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler._parse_pll_register", "name": "_parse_pll_register", "type": null}}, "_set_default_pll_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler._set_default_pll_values", "name": "_set_default_pll_values", "type": null}}, "_write_pll_config_to_registers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pll_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler._write_pll_config_to_registers", "name": "_write_pll_config_to_registers", "type": null}}, "get_all_pll_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler.get_all_pll_status", "name": "get_all_pll_status", "type": null}}, "get_pll_frequency": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pll_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler.get_pll_frequency", "name": "get_pll_frequency", "type": null}}, "get_pll_lock_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pll_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler.get_pll_lock_status", "name": "get_pll_lock_status", "type": null}}, "get_pll_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pll_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler.get_pll_mode", "name": "get_pll_mode", "type": null}}, "pll_configs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler.pll_configs", "name": "pll_configs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pll_frequency_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler.pll_frequency_changed", "name": "pll_frequency_changed", "type": "PyQt5.QtCore.pyqtSignal"}}, "pll_lock_status_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler.pll_lock_status_changed", "name": "pll_lock_status_changed", "type": "PyQt5.QtCore.pyqtSignal"}}, "pll_mode_changed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler.pll_mode_changed", "name": "pll_mode_changed", "type": "PyQt5.QtCore.pyqtSignal"}}, "reset_pll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pll_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler.reset_pll", "name": "reset_pll", "type": null}}, "set_pll_frequency": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "pll_name", "frequency"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler.set_pll_frequency", "name": "set_pll_frequency", "type": null}}, "set_pll_mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "pll_name", "mode"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler.set_pll_mode", "name": "set_pll_mode", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "ui.handlers.ModernPLLControlHandler.ModernPLLControlHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "QObject": {".class": "SymbolTableNode", "cross_ref": "PyQt5.QtCore.QObject", "kind": "Gdef"}, "QWidget": {".class": "SymbolTableNode", "cross_ref": "PyQt5.QtWidgets.QWidget", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ui.handlers.ModernPLLControlHandler.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ui.handlers.ModernPLLControlHandler.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ui.handlers.ModernPLLControlHandler.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ui.handlers.ModernPLLControlHandler.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ui.handlers.ModernPLLControlHandler.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "ui.handlers.ModernPLLControlHandler.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "ui.handlers.ModernPLLControlHandler.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "pyqtSignal": {".class": "SymbolTableNode", "cross_ref": "PyQt5.QtCore.pyqtSignal", "kind": "Gdef"}}, "path": "E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\ModernPLLControlHandler.py"}