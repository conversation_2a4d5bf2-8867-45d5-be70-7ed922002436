# FSJ04832寄存器配置工具依赖项
# FSJ04832 Register Configuration Tool Dependencies

# 核心GUI框架 / Core GUI Framework
PyQt5>=5.15.0

# 系统监控和进程管理 / System monitoring and process management
psutil>=5.8.0

# 数据处理 / Data processing
numpy>=1.19.0

# 串行通信 / Serial communication
pyserial>=3.5

# 配置文件处理 / Configuration file handling
pyyaml>=5.4.0

# 日志增强 / Enhanced logging
colorlog>=6.6.0

# 类型提示支持 / Type hints support (for Python < 3.8)
typing-extensions>=3.10.0

# 路径处理 / Path handling (for Python < 3.4)
pathlib2>=2.3.0; python_version < "3.4"

# 开发和测试依赖 / Development and testing dependencies
pytest>=6.2.0
pytest-qt>=4.0.0
pytest-cov>=2.12.0
mock>=4.0.0

# 打包和分发 / Packaging and distribution
pyinstaller>=4.5.0
setuptools>=57.0.0
wheel>=0.36.0

# 代码质量 / Code quality
flake8>=3.9.0
black>=21.0.0

# 文档生成 / Documentation generation
sphinx>=4.0.0
sphinx-rtd-theme>=0.5.0

# 可选依赖 / Optional dependencies
# 如果需要更高级的数据分析功能 / For advanced data analysis features
# pandas>=1.3.0
# matplotlib>=3.4.0

# 如果需要网络功能 / For network features
# requests>=2.25.0
# urllib3>=1.26.0

# 如果需要数据库支持 / For database support
# sqlite3  # 内置模块 / Built-in module

# 平台特定依赖 / Platform-specific dependencies
# Windows特定 / Windows-specific
pywin32>=227; sys_platform == "win32"

# Linux特定 / Linux-specific
# python3-dev; sys_platform == "linux"

# macOS特定 / macOS-specific
# pyobjc-framework-Cocoa>=7.0; sys_platform == "darwin"
