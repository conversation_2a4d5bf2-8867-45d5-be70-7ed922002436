{"data_mtime": 1753840996, "dep_lines": [3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["PyQt5.QtCore", "utils.Log", "builtins", "PyQt5", "PyQt5.sip", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "types", "typing", "utils"], "hash": "1c2c3f7603a65433b97aed0e0c5e4e6844c60d4b", "id": "core.event_bus.RegisterUpdateBus", "ignore_all": false, "interface_hash": "3935f9d55c41439ce6d860b95ac9dce519d70048", "mtime": 1753434477, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\event_bus\\RegisterUpdateBus.py", "plugin_data": null, "size": 32166, "suppressed": [], "version_id": "1.15.0"}