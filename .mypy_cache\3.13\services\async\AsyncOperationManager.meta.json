{"data_mtime": 1753860409, "dep_lines": [9, 11, 12, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["concurrent.futures", "PyQt5.QtCore", "utils.Log", "typing", "builtins", "PyQt5", "PyQt5.sip", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "concurrent", "concurrent.futures._base", "concurrent.futures.thread", "utils"], "hash": "e855a04a8a4612c357f8401e672b1642df7551cc", "id": "services.async.AsyncOperationManager", "ignore_all": false, "interface_hash": "bff8ceb0bd1dacdb47f17f4388d36f84428596db", "mtime": 1753860407, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\async\\AsyncOperationManager.py", "plugin_data": null, "size": 7863, "suppressed": [], "version_id": "1.15.0"}