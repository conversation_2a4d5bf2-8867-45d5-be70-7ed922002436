{".class": "MypyFile", "_fullname": "organize_main_directory_docs", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DocumentOrganizer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "organize_main_directory_docs.DocumentOrganizer", "name": "DocumentOrganizer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "organize_main_directory_docs.DocumentOrganizer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "organize_main_directory_docs", "mro": ["organize_main_directory_docs.DocumentOrganizer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "project_root"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "organize_main_directory_docs.DocumentOrganizer.__init__", "name": "__init__", "type": null}}, "categorize_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "doc_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "organize_main_directory_docs.DocumentOrganizer.categorize_document", "name": "categorize_document", "type": null}}, "create_category_directories": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "organize_main_directory_docs.DocumentOrganizer.create_category_directories", "name": "create_category_directories", "type": null}}, "create_main_docs_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "organized_docs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "organize_main_directory_docs.DocumentOrganizer.create_main_docs_index", "name": "create_main_docs_index", "type": null}}, "doc_categories": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "organize_main_directory_docs.DocumentOrganizer.doc_categories", "name": "doc_categories", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "docs_dir": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "organize_main_directory_docs.DocumentOrganizer.docs_dir", "name": "docs_dir", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "move_document": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "doc_path", "category"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "organize_main_directory_docs.DocumentOrganizer.move_document", "name": "move_document", "type": null}}, "organize_documents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "organize_main_directory_docs.DocumentOrganizer.organize_documents", "name": "organize_documents", "type": null}}, "project_root": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "organize_main_directory_docs.DocumentOrganizer.project_root", "name": "project_root", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "scan_main_directory_docs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "organize_main_directory_docs.DocumentOrganizer.scan_main_directory_docs", "name": "scan_main_directory_docs", "type": null}}, "update_category_readme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "category", "doc_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "organize_main_directory_docs.DocumentOrganizer.update_category_readme", "name": "update_category_readme", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "organize_main_directory_docs.DocumentOrganizer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "organize_main_directory_docs.DocumentOrganizer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "organize_main_directory_docs.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "organize_main_directory_docs.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "organize_main_directory_docs.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "organize_main_directory_docs.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "organize_main_directory_docs.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "organize_main_directory_docs.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "organize_main_directory_docs.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "organize_main_directory_docs.main", "name": "main", "type": null}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef"}, "success": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "organize_main_directory_docs.success", "name": "success", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "path": "E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\docs\\organize_main_directory_docs.py"}