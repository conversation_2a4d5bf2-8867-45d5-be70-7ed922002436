# 信息面板日志级别控制使用说明

## 🎯 功能概述

在信息面板的"调试日志"标签页中新增了日志级别控制功能，可以实时过滤和控制日志显示。

## 🎨 界面布局

```
┌─ 日志控制 ─────────────────────────────────┐
│ 日志级别: [⚠️ WARNING ▼] [✓实时更新]       │
│          [应用级别到系统] [测试过滤]        │
└────────────────────────────────────────────┘
┌─ 调试日志 ─────────────────────────────────┐
│                                            │
│           过滤后的日志内容                 │
│                                            │
└────────────────────────────────────────────┘
│ [清空日志] [刷新日志]                      │
└────────────────────────────────────────────┘
```

## 📋 使用步骤

### 1. 打开信息面板
- 在主应用程序中打开信息面板
- 切换到"调试日志"标签页

### 2. 选择日志级别
在"日志控制"区域的下拉框中选择：
- 🔴 **ERROR** - 只显示错误（最少日志）
- ⚠️ **WARNING** - 显示警告和错误（推荐）
- ℹ️ **INFO** - 显示信息、警告和错误
- 🔍 **DEBUG** - 显示所有日志（最详细）

### 3. 控制实时更新
- ✅ **启用实时更新**：日志会自动刷新
- ❌ **禁用实时更新**：需要手动点击"刷新日志"

### 4. 测试功能
- 点击"**测试过滤**"按钮生成测试日志
- 观察不同级别的过滤效果

### 5. 应用到系统
- 点击"**应用级别到系统**"按钮
- 将选择的日志级别应用到整个应用程序
- 自动更新配置文件

## 🔧 功能特性

### ✅ 实时过滤
- 选择日志级别后立即过滤显示
- 根据日志行中的级别标识进行智能过滤
- 保留无级别标识的日志行

### ✅ 性能优化
- 可关闭实时更新节省系统资源
- 只显示最近100行日志
- 智能缓存机制

### ✅ 系统集成
- 与现有日志控制工具完全兼容
- 使用相同的配置文件
- 支持动态级别切换

## 🧪 测试方法

### 方法1：使用测试按钮
1. 选择一个日志级别（如WARNING）
2. 点击"测试过滤"按钮
3. 观察日志显示是否只显示WARNING及以上级别

### 方法2：手动验证
1. 运行应用程序产生各种级别的日志
2. 在信息面板中切换不同级别
3. 观察日志显示的变化

### 方法3：使用外部工具生成日志
```bash
# 生成测试日志
python utils/log_control.py DEBUG
# 然后在信息面板中观察过滤效果
```

## 🔍 故障排除

### 问题1：过滤不起作用
**可能原因**：
- 日志级别下拉框未正确初始化
- 信号连接失败

**解决方法**：
1. 点击"刷新日志"按钮手动刷新
2. 重新选择日志级别
3. 点击"测试过滤"验证功能

### 问题2：日志显示为空
**可能原因**：
- 日志文件不存在
- 选择的级别过高，过滤掉了所有日志

**解决方法**：
1. 检查 `log/app.log` 文件是否存在
2. 选择DEBUG级别查看所有日志
3. 确保应用程序正在产生日志

### 问题3：实时更新不工作
**可能原因**：
- 实时更新开关被禁用
- 定时器未正确启动

**解决方法**：
1. 确保"实时更新"复选框被选中
2. 手动点击"刷新日志"按钮
3. 重新打开信息面板

## 💡 使用技巧

### 1. 日常使用建议
- **开发调试**：使用DEBUG或INFO级别
- **日常使用**：使用WARNING级别（推荐）
- **性能优先**：使用ERROR级别

### 2. 性能优化
- 在不需要实时查看日志时，关闭"实时更新"
- 使用较高的日志级别减少显示内容
- 定期清空日志显示

### 3. 调试技巧
- 使用"测试过滤"快速验证功能
- 结合"应用级别到系统"统一管理日志
- 查看文件日志获取完整信息

## 🔗 相关工具

- `utils/log_control.py` - 命令行日志控制工具
- `set_log_level.bat` - 批处理日志控制界面
- `config/default.json` - 日志配置文件
- `log/app.log` - 完整日志文件

## 📞 技术支持

如果遇到问题，请：
1. 检查控制台是否有错误信息
2. 查看 `log/app.log` 文件中的详细日志
3. 尝试重启应用程序
4. 使用"测试过滤"按钮验证功能状态
