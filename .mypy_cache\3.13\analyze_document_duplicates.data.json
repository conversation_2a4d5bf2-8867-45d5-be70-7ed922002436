{".class": "MypyFile", "_fullname": "analyze_document_duplicates", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DocumentDuplicateAnalyzer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "analyze_document_duplicates.DocumentDuplicateAnalyzer", "name": "DocumentDuplicateAnalyzer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "analyze_document_duplicates.DocumentDuplicateAnalyzer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "analyze_document_duplicates", "mro": ["analyze_document_duplicates.DocumentDuplicateAnalyzer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "docs_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "analyze_document_duplicates.DocumentDuplicateAnalyzer.__init__", "name": "__init__", "type": null}}, "_analyze_content_overlap": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "analyze_document_duplicates.DocumentDuplicateAnalyzer._analyze_content_overlap", "name": "_analyze_content_overlap", "type": null}}, "_calculate_priority": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "analysis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "analyze_document_duplicates.DocumentDuplicateAnalyzer._calculate_priority", "name": "_calculate_priority", "type": null}}, "_extract_keywords": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "analyze_document_duplicates.DocumentDuplicateAnalyzer._extract_keywords", "name": "_extract_keywords", "type": null}}, "_extract_sections": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "analyze_document_duplicates.DocumentDuplicateAnalyzer._extract_sections", "name": "_extract_sections", "type": null}}, "_extract_title": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "analyze_document_duplicates.DocumentDuplicateAnalyzer._extract_title", "name": "_extract_title", "type": null}}, "_generate_consolidation_suggestions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "known_analysis"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "analyze_document_duplicates.DocumentDuplicateAnalyzer._generate_consolidation_suggestions", "name": "_generate_consolidation_suggestions", "type": null}}, "_generate_merge_suggestion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "group_name", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "analyze_document_duplicates.DocumentDuplicateAnalyzer._generate_merge_suggestion", "name": "_generate_merge_suggestion", "type": null}}, "analyze_document_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "analyze_document_duplicates.DocumentDuplicateAnalyzer.analyze_document_content", "name": "analyze_document_content", "type": null}}, "analyze_known_duplicates": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "analyze_document_duplicates.DocumentDuplicateAnalyzer.analyze_known_duplicates", "name": "analyze_known_duplicates", "type": null}}, "docs_dir": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "analyze_document_duplicates.DocumentDuplicateAnalyzer.docs_dir", "name": "docs_dir", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "duplicate_patterns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "analyze_document_duplicates.DocumentDuplicateAnalyzer.duplicate_patterns", "name": "duplicate_patterns", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "find_duplicate_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "documents_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "analyze_document_duplicates.DocumentDuplicateAnalyzer.find_duplicate_content", "name": "find_duplicate_content", "type": null}}, "generate_consolidation_report": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "analyze_document_duplicates.DocumentDuplicateAnalyzer.generate_consolidation_report", "name": "generate_consolidation_report", "type": null}}, "known_duplicates": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "analyze_document_duplicates.DocumentDuplicateAnalyzer.known_duplicates", "name": "known_duplicates", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "scan_all_documents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "analyze_document_duplicates.DocumentDuplicateAnalyzer.scan_all_documents", "name": "scan_all_documents", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "analyze_document_duplicates.DocumentDuplicateAnalyzer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "analyze_document_duplicates.DocumentDuplicateAnalyzer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "analyze_document_duplicates.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "analyze_document_duplicates.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "analyze_document_duplicates.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "analyze_document_duplicates.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "analyze_document_duplicates.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "analyze_document_duplicates.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "analyze_document_duplicates.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "analyze_document_duplicates.main", "name": "main", "type": null}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "success": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "analyze_document_duplicates.success", "name": "success", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "path": "E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\docs\\analyze_document_duplicates.py"}