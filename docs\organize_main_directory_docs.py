#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主目录文档整理工具
将主目录下散乱的文档文件整理到合适的目录结构中
"""

import os
import shutil
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DocumentOrganizer:
    """文档整理器"""
    
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.docs_dir = self.project_root / "docs"
        
        # 确保docs目录存在
        self.docs_dir.mkdir(exist_ok=True)
        
        # 定义文档分类规则
        self.doc_categories = {
            "features": {
                "description": "功能特性相关文档",
                "patterns": [
                    "*VCODistFreq*",
                    "*PLL2*",
                    "*SYSREF*",
                    "*InternalVCOFreq*",
                    "*Fin0模式*",
                    "*缓存机制*",
                    "*同步功能*",
                    "*自动调整*"
                ]
            },
            "fixes": {
                "description": "问题修复相关文档", 
                "patterns": [
                    "*修复*",
                    "*修正*",
                    "*解决方案*",
                    "*问题*",
                    "*调试*",
                    "*重复函数*"
                ]
            },
            "implementation": {
                "description": "实现说明文档",
                "patterns": [
                    "*实现*",
                    "*添加*",
                    "*移除*",
                    "*说明*"
                ]
            },
            "readme": {
                "description": "README文档",
                "patterns": [
                    "README*"
                ]
            },
            "usage": {
                "description": "使用说明文档",
                "patterns": [
                    "*使用说明*",
                    "*控制使用*"
                ]
            }
        }
        
    def scan_main_directory_docs(self):
        """扫描主目录下的文档文件"""
        main_docs = []
        
        for file_path in self.project_root.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in ['.md', '.txt', '.rst']:
                # 排除已经在docs目录中的文件
                if file_path.parent != self.docs_dir:
                    main_docs.append(file_path)
                    
        return main_docs
        
    def categorize_document(self, doc_path):
        """根据文件名对文档进行分类"""
        file_name = doc_path.name.lower()
        
        for category, info in self.doc_categories.items():
            for pattern in info["patterns"]:
                pattern_lower = pattern.lower().replace("*", "")
                if pattern_lower in file_name:
                    return category
                    
        # 默认分类
        return "misc"
        
    def create_category_directories(self):
        """创建分类目录"""
        for category, info in self.doc_categories.items():
            category_dir = self.docs_dir / category
            category_dir.mkdir(exist_ok=True)
            
            # 创建分类说明文件
            readme_file = category_dir / "README.md"
            if not readme_file.exists():
                with open(readme_file, 'w', encoding='utf-8') as f:
                    f.write(f"# {category.title()}\n\n")
                    f.write(f"{info['description']}\n\n")
                    f.write("## 文档列表\n\n")
                    
        # 创建misc目录
        misc_dir = self.docs_dir / "misc"
        misc_dir.mkdir(exist_ok=True)
        
    def move_document(self, doc_path, category):
        """移动文档到指定分类目录"""
        try:
            target_dir = self.docs_dir / category
            target_path = target_dir / doc_path.name
            
            # 如果目标文件已存在，添加序号
            counter = 1
            original_target = target_path
            while target_path.exists():
                stem = original_target.stem
                suffix = original_target.suffix
                target_path = target_dir / f"{stem}_{counter}{suffix}"
                counter += 1
                
            shutil.move(str(doc_path), str(target_path))
            logger.info(f"移动文档: {doc_path.name} -> {category}/{target_path.name}")
            return target_path
            
        except Exception as e:
            logger.error(f"移动文档失败 {doc_path.name}: {str(e)}")
            return None
            
    def update_category_readme(self, category, doc_name):
        """更新分类README文件"""
        try:
            readme_file = self.docs_dir / category / "README.md"
            
            # 读取现有内容
            content = ""
            if readme_file.exists():
                with open(readme_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    
            # 添加文档链接
            doc_link = f"- [{doc_name}](./{doc_name})\n"
            
            if "## 文档列表" in content:
                # 在文档列表部分添加
                lines = content.split('\n')
                insert_index = -1
                for i, line in enumerate(lines):
                    if line.strip() == "## 文档列表":
                        insert_index = i + 2
                        break
                        
                if insert_index > 0:
                    lines.insert(insert_index, doc_link.strip())
                    content = '\n'.join(lines)
                else:
                    content += f"\n{doc_link}"
            else:
                content += f"\n## 文档列表\n\n{doc_link}"
                
            # 写回文件
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write(content)
                
        except Exception as e:
            logger.error(f"更新分类README失败 {category}: {str(e)}")
            
    def create_main_docs_index(self, organized_docs):
        """创建主文档索引"""
        try:
            index_file = self.docs_dir / "MAIN_DOCS_INDEX.md"
            
            with open(index_file, 'w', encoding='utf-8') as f:
                f.write("# 主目录文档整理索引\n\n")
                f.write("本文档记录了从主目录整理到docs目录的所有文档。\n\n")
                f.write(f"整理时间: {Path(__file__).stat().st_mtime}\n")
                f.write(f"整理文档数量: {len(organized_docs)}\n\n")
                
                # 按分类列出文档
                categories = {}
                for doc_info in organized_docs:
                    category = doc_info['category']
                    if category not in categories:
                        categories[category] = []
                    categories[category].append(doc_info)
                    
                for category, docs in categories.items():
                    f.write(f"## {category.title()}\n\n")
                    f.write(f"{self.doc_categories.get(category, {}).get('description', '其他文档')}\n\n")
                    
                    for doc_info in docs:
                        f.write(f"- [{doc_info['name']}](./{category}/{doc_info['name']}) ")
                        f.write(f"(原路径: {doc_info['original_path']})\n")
                    f.write("\n")
                    
            logger.info(f"创建主文档索引: {index_file}")
            
        except Exception as e:
            logger.error(f"创建主文档索引失败: {str(e)}")
            
    def organize_documents(self):
        """执行文档整理"""
        logger.info("开始整理主目录文档...")
        
        # 扫描主目录文档
        main_docs = self.scan_main_directory_docs()
        logger.info(f"发现主目录文档: {len(main_docs)}个")
        
        if not main_docs:
            logger.info("主目录没有需要整理的文档")
            return
            
        # 创建分类目录
        self.create_category_directories()
        
        # 整理文档
        organized_docs = []
        
        for doc_path in main_docs:
            # 确定分类
            category = self.categorize_document(doc_path)
            
            # 移动文档
            target_path = self.move_document(doc_path, category)
            
            if target_path:
                # 记录整理信息
                doc_info = {
                    'name': target_path.name,
                    'category': category,
                    'original_path': str(doc_path),
                    'new_path': str(target_path)
                }
                organized_docs.append(doc_info)
                
                # 更新分类README
                self.update_category_readme(category, target_path.name)
                
        # 创建主文档索引
        self.create_main_docs_index(organized_docs)
        
        logger.info(f"文档整理完成! 共整理 {len(organized_docs)} 个文档")
        
        # 输出整理结果
        print("\n" + "="*60)
        print("📚 主目录文档整理结果")
        print("="*60)
        
        categories = {}
        for doc_info in organized_docs:
            category = doc_info['category']
            if category not in categories:
                categories[category] = []
            categories[category].append(doc_info['name'])
            
        for category, docs in categories.items():
            print(f"\n📁 {category.title()} ({len(docs)}个文档):")
            for doc_name in docs:
                print(f"   - {doc_name}")
                
        print(f"\n✅ 总计整理文档: {len(organized_docs)}个")
        print(f"📍 文档位置: docs/")
        print(f"📋 详细索引: docs/MAIN_DOCS_INDEX.md")

def main():
    """主函数"""
    try:
        organizer = DocumentOrganizer()
        organizer.organize_documents()
        
    except Exception as e:
        logger.error(f"文档整理失败: {str(e)}")
        return False
        
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 文档整理成功完成!")
    else:
        print("\n❌ 文档整理失败!")
