{"data_mtime": 1750822252, "dep_lines": [942, 14, 10, 12, 13, 15, 205, 943, 944, 965, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [20, 5, 5, 5, 5, 5, 20, 20, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["core.services.register.RegisterManager", "ui.handlers.ModernBaseHandler", "PyQt5.QtWidgets", "PyQt5.QtCore", "PyQt5.QtGui", "utils.Log", "traceback", "json", "os", "sys", "builtins", "PyQt5", "PyQt5.sip", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "core", "core.services", "core.services.register", "io", "json.decoder", "types", "typing", "utils"], "hash": "603b1bcd79a67b7a9e9141b7d712ed0052d153b8", "id": "ui.handlers.ModernRegisterTableHandler", "ignore_all": false, "interface_hash": "fdf6e1ef78228252fa6359bfc9f3cd4c642f9727", "mtime": 1751527655, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\ModernRegisterTableHandler.py", "plugin_data": null, "size": 41531, "suppressed": [], "version_id": "1.15.0"}