{".class": "MypyFile", "_fullname": "services.register.RegisterManager", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Register": {".class": "SymbolTableNode", "cross_ref": "core.models.RegisterModel.Register", "kind": "Gdef"}, "RegisterAdapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "services.register.RegisterManager.RegisterAdapter", "name": "RegisterAdapter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterAdapter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "services.register.RegisterManager", "mro": ["services.register.RegisterManager.RegisterAdapter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "address", "register", "reg_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterAdapter.__init__", "name": "__init__", "type": null}}, "address": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "services.register.RegisterManager.RegisterAdapter.address", "name": "address", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "bits": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "services.register.RegisterManager.RegisterAdapter.bits", "name": "bits", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "get_bit_field_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "bit_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterAdapter.get_bit_field_value", "name": "get_bit_field_value", "type": null}}, "reg_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "services.register.RegisterManager.RegisterAdapter.reg_info", "name": "reg_info", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "register": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "services.register.RegisterManager.RegisterAdapter.register", "name": "register", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "set_bit_field_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "bit_name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterAdapter.set_bit_field_value", "name": "set_bit_field_value", "type": null}}, "value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "services.register.RegisterManager.RegisterAdapter.value", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "services.register.RegisterManager.RegisterAdapter.value", "name": "value", "type": null}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "services.register.RegisterManager.RegisterAdapter.value", "name": "value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["services.register.RegisterManager.RegisterAdapter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "value of RegisterAdapter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "new_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "services.register.RegisterManager.RegisterAdapter.value", "name": "value", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "value", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["services.register.RegisterManager.RegisterAdapter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "value", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "services.register.RegisterManager.RegisterAdapter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "services.register.RegisterManager.RegisterAdapter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RegisterManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "services.register.RegisterManager.RegisterManager", "name": "RegisterManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "services.register.RegisterManager", "mro": ["services.register.RegisterManager.RegisterManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "registers_json"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager.__init__", "name": "__init__", "type": null}}, "_emit_register_update_signal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "addr", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager._emit_register_update_signal", "name": "_emit_register_update_signal", "type": null}}, "_initialize_registers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager._initialize_registers", "name": "_initialize_registers", "type": null}}, "_normalize_register_address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "addr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager._normalize_register_address", "name": "_normalize_register_address", "type": null}}, "_validate_and_normalize_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "registers_json"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager._validate_and_normalize_config", "name": "_validate_and_normalize_config", "type": null}}, "get_adapted_register_objects": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager.get_adapted_register_objects", "name": "get_adapted_register_objects", "type": null}}, "get_all_register_addresses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager.get_all_register_addresses", "name": "get_all_register_addresses", "type": null}}, "get_all_register_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager.get_all_register_values", "name": "get_all_register_values", "type": null}}, "get_all_registers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager.get_all_registers", "name": "get_all_registers", "type": null}}, "get_bit_field_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "addr", "bit_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager.get_bit_field_value", "name": "get_bit_field_value", "type": null}}, "get_register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "addr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager.get_register", "name": "get_register", "type": null}}, "get_register_bit_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "addr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager.get_register_bit_info", "name": "get_register_bit_info", "type": null}}, "get_register_bits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "addr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager.get_register_bits", "name": "get_register_bits", "type": null}}, "get_register_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "addr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager.get_register_info", "name": "get_register_info", "type": null}}, "get_register_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "addr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager.get_register_value", "name": "get_register_value", "type": null}}, "get_registers_by_widget_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "widget_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager.get_registers_by_widget_names", "name": "get_registers_by_widget_names", "type": null}}, "get_widget_register_mapping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "addr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager.get_widget_register_mapping", "name": "get_widget_register_mapping", "type": null}}, "print_register_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager.print_register_info", "name": "print_register_info", "type": null}}, "register_objects": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "services.register.RegisterManager.RegisterManager.register_objects", "name": "register_objects", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "registers_json": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "services.register.RegisterManager.RegisterManager.registers_json", "name": "registers_json", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "search_bit_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "keyword"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager.search_bit_fields", "name": "search_bit_fields", "type": null}}, "set_bit_field_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "addr", "bit_name", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager.set_bit_field_value", "name": "set_bit_field_value", "type": null}}, "set_register_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "addr", "value", "force_update"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager.set_register_value", "name": "set_register_value", "type": null}}, "update_widget_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "widget_name", "value", "addr_hint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "services.register.RegisterManager.RegisterManager.update_widget_value", "name": "update_widget_value", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "services.register.RegisterManager.RegisterManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "services.register.RegisterManager.RegisterManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "services.register.RegisterManager.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "services.register.RegisterManager.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "services.register.RegisterManager.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "services.register.RegisterManager.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "services.register.RegisterManager.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "services.register.RegisterManager.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "get_module_logger": {".class": "SymbolTableNode", "cross_ref": "utils.Log.get_module_logger", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "services.register.RegisterManager.logger", "name": "logger", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "path": "E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\register\\RegisterManager.py"}