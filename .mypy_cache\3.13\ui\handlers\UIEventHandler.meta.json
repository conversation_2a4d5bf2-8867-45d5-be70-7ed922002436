{"data_mtime": 1750155115, "dep_lines": [229, 9, 10, 6, 7, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [20, 5, 5, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["core.services.version.VersionService", "PyQt5.QtWidgets", "utils.Log", "os", "sys", "traceback", "builtins", "PyQt5", "PyQt5.QtCore", "PyQt5.QtGui", "PyQt5.sip", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "core", "core.services", "core.services.version", "genericpath", "types", "typing", "utils"], "hash": "a44292d8a08f964746a277630c795fddef945b81", "id": "ui.handlers.UIEventHandler", "ignore_all": false, "interface_hash": "8607c189050b3cef977412f8988fd3805c25bbff", "mtime": 1749113202, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\UIEventHandler.py", "plugin_data": null, "size": 12800, "suppressed": [], "version_id": "1.15.0"}