{"data_mtime": 1752748334, "dep_lines": [9, 774, 6, 7, 8, 127, 168, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 20, 5, 5, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["core.event_bus.RegisterUpdateBus", "core.services.BatchOperationState", "PyQt5.QtWidgets", "PyQt5.QtCore", "utils.Log", "random", "traceback", "builtins", "PyQt5", "PyQt5.QtGui", "PyQt5.sip", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "core.event_bus", "types", "typing", "utils"], "hash": "224f15a1c601b4907860c497eeba00af3dc751a9", "id": "core.services.register.RegisterOperationService", "ignore_all": false, "interface_hash": "dec455172bef51f2df80dfa29ecee5c6a4414fd8", "mtime": 1752748490, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\register\\RegisterOperationService.py", "plugin_data": null, "size": 30601, "suppressed": [], "version_id": "1.15.0"}