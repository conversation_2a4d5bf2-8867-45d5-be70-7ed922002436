{"data_mtime": 1754299886, "dep_lines": [7, 8, 156, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["sys", "os", "traceback", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "typing"], "hash": "1c11cc6631601bf2cf311b3db8f5cfe2fffdc133", "id": "test_vco_dist_freq_fix", "ignore_all": false, "interface_hash": "7fb77a766f8de96aaab61c8fe307c4d33f835d63", "mtime": 1754299885, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\test_vco_dist_freq_fix.py", "plugin_data": null, "size": 6175, "suppressed": [], "version_id": "1.15.0"}