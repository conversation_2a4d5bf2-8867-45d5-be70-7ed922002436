# FSJ04832 寄存器配置工具

一个用于配置和管理FSJ04832设备寄存器的图形界面工具。

## 项目结构

项目已经进行了代码重构，采用模块化的设计，主要组件包括：

- **UiPage/** - 包含所有UI相关的处理器类
  - `RegisterTreeHandler.py` - 处理寄存器树视图
  - `RegisterTableHandler.py` - 处理寄存器表格和位字段显示
  - `RegisterIOHandler.py` - 处理寄存器输入/输出操作
  - `RegisterManager.py` - 管理寄存器对象和操作
  - `RegisterMainWindow.py` - 集成以上组件的主窗口类
  - 其他功能模块 (ClkOutputsHandler, SyncSysRefHandler等)
- **lib/** - 包含寄存器定义和其他库文件
- **utils/** - 包含实用工具类和函数
- **images/** - 包含应用程序图像资源
- **main.py** - 新的应用程序入口点

## 安装

1. 确保已安装Python 3.8或更高版本
2. 安装所需的依赖项：

```
pip install PyQt5 pyserial
```

## 运行

使用以下命令启动应用程序：

```
python main.py
```

或者在Windows环境中双击`main.py`文件。

## 功能说明

- **寄存器浏览** - 通过树形结构浏览所有寄存器
- **寄存器配置** - 查看和修改寄存器的位字段值
- **批量操作** - 支持读取和写入所有寄存器
- **配置保存/加载** - 保存和加载寄存器配置
- **SPI通信** - 通过串口与设备进行SPI通信
- **模拟模式** - 支持不连接设备的模拟操作

## 重构说明

本项目已经从之前的单一文件结构重构为模块化设计，主要改进点包括：

1. **代码分离** - 将UI、业务逻辑和数据访问分离成独立模块
2. **接口抽象** - 使用信号/槽机制实现模块间通信
3. **功能封装** - 通过专用的处理器类封装相关功能
4. **资源管理** - 改进资源加载和错误处理
5. **可维护性** - 提高代码可读性和可维护性

## 重构进展

目前已经完成的重构工作包括：

1. **模块化架构**：
   - 将原来单一的`fsjReadback.py`文件拆分为多个模块
   - 创建了专门的`main.py`作为应用程序入口点
   - 实现了`RegisterMainWindow`作为主窗口类，集成各功能组件

2. **组件抽象**：
   - `RegisterManager`：负责管理所有寄存器数据和状态
   - `RegisterTableHandler`：处理寄存器位域表格显示和交互
   - `RegisterTreeHandler`：处理寄存器树形结构显示和导航
   - `RegisterIOHandler`：处理读写操作和用户输入

3. **SPI通信重构**：
   - 创建`spi_operations.py`模块，封装了所有SPI通信功能
   - 支持异步操作和错误处理
   - 实现了模拟模式，便于在无硬件环境下测试

4. **批处理优化**：
   - 改进了批量读写寄存器的处理逻辑
   - 引入进度显示和取消机制
   - 优化了批处理性能和内存使用

5. **寄存器更新总线**：
   - 实现了`RegisterUpdateBus`单例类
   - 提供全局事件机制，用于跨组件通知寄存器状态变更
   - 减少了组件间的紧耦合

6. **错误处理**：
   - 统一的异常处理机制
   - 用户友好的错误提示
   - 完善的日志记录功能

7. **核心功能测试**：
   - 验证了寄存器读写功能
   - 测试了批量操作
   - 确保了模拟模式正常工作

后续工作将继续完善工具窗口和高级功能的实现。

## 如何开发

1. 使用`UiPage/RegisterMainWindow.py`作为主窗口类
2. 添加新功能时，请遵循现有的模块化设计
3. 使用`RegisterUpdateBus`来广播寄存器更新事件

## 使用说明

1. 启动应用程序后，首先选择COM端口
2. 在左侧树形视图中选择要操作的寄存器
3. 在右侧面板中查看和修改寄存器值
4. 使用底部按钮执行读取、写入等操作

## 注意事项

- 修改只读位会出现警告提示
- 在模拟模式下，所有操作不会真正写入设备
- SPI操作有5秒超时，如果设备无响应将显示错误 