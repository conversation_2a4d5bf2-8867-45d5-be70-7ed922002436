# 修复SYSREF Divider模式VCODistFreq计算

## 问题描述

用户反馈当FBMUX选择为"SYSREF Divider"时，VCO Dist频率的计算是不对的。正确的计算公式应该是：

**VCODistFreq = PLL2PFDFreq × PLL2NDivider × SysrefDiv**

## 问题分析

### 原始问题
在原始代码中，当FBMUX=2（SYSREF Divider）时：
```python
elif fb_mux_value == 1:  # CLKout8
    feedback_divider = self._get_dclk8_9div_value()
    logger.debug(f"反馈模式使用CLKout8，分频比: {feedback_divider}")
else:
    logger.warning(f"反馈模式下未知的FB_MUX值: {fb_mux_value}")
    feedback_divider = 1.0  # ❌ 错误：忽略了SYSREF分频器
```

这导致当FBMUX选择SYSREF Divider时，`feedback_divider`被设置为1.0，完全忽略了SYSREF分频器的值。

### 正确的计算逻辑
当FBMUX=SYSREF Divider时，应该：
1. 获取同步系统参考窗口中的SYSREF分频器值
2. 使用公式：`VCODistFreq = PLL2PFDFreq × PLL2NDivider × SysrefDiv`

## 实施的修复

### 1. 修复主要的VCODistFreq计算方法

在`_calculate_vco_dist_freq()`方法中添加了SYSREF Divider的处理：

```python
if fb_mux_value == 0:  # CLKout6
    feedback_divider = self._get_dclk6_7div_value()
    logger.debug(f"反馈模式使用CLKout6，分频比: {feedback_divider}")
elif fb_mux_value == 1:  # CLKout8
    feedback_divider = self._get_dclk8_9div_value()
    logger.debug(f"反馈模式使用CLKout8，分频比: {feedback_divider}")
elif fb_mux_value == 2:  # SYSREF Divider ✅ 新增
    feedback_divider = self._get_sysref_divider_value()
    logger.debug(f"反馈模式使用SYSREF Divider，分频比: {feedback_divider}")
else:
    logger.warning(f"反馈模式下未知的FB_MUX值: {fb_mux_value}")
    feedback_divider = 1.0
```

### 2. 添加SYSREF分频器值获取方法

新增了`_get_sysref_divider_value()`方法：

```python
def _get_sysref_divider_value(self):
    """获取SYSREF分频器的值
    
    从同步系统参考窗口获取SYSREF_DIV的值
    
    Returns:
        float: SYSREF分频器值，默认为1.0
    """
    try:
        # 尝试从主窗口获取同步系统参考窗口
        main_window = self._get_main_window()
        if main_window and hasattr(main_window, 'sync_sysref_window') and main_window.sync_sysref_window:
            sync_window = main_window.sync_sysref_window
            if hasattr(sync_window.ui, 'spinBoxSysrefDIV'):
                sysref_div = sync_window.ui.spinBoxSysrefDIV.value()
                if sysref_div > 0:
                    logger.debug(f"从同步系统参考窗口获取SYSREF分频器值: {sysref_div}")
                    return float(sysref_div)
                else:
                    logger.warning("SYSREF分频器值为0或负数，使用默认值1")
                    return 1.0
            else:
                logger.warning("同步系统参考窗口中未找到spinBoxSysrefDIV控件")
        else:
            logger.warning("同步系统参考窗口未打开或不存在")

        # 如果无法获取，返回默认值1
        return 1.0

    except Exception as e:
        logger.error(f"获取SYSREF分频器值时出错: {str(e)}")
        return 1.0
```

### 3. 修复简化版本的VCODistFreq计算

在`_update_vco_dist_freq_simplified()`方法中也添加了相同的修复：

```python
elif pll2_nclk_mux == 1:
    # 模式1: Feedback Mux模式
    # 需要根据FBMUX值选择不同的计算方法
    fb_mux_value = 0
    if hasattr(self.ui, "FBMUX"):
        fb_mux_value = self.ui.FBMUX.currentData() if hasattr(self.ui.FBMUX, 'currentData') else self.ui.FBMUX.currentIndex()
    
    if fb_mux_value == 2:  # SYSREF Divider ✅ 新增处理
        # VCODistFreq = PLL2PFDFreq × PLL2NDivider × SysrefDiv
        sysref_div = self._get_sysref_divider_value()
        if pll2_pfd_freq > 0 and sysref_div > 0:
            vco_dist_freq = pll2_pfd_freq * n_divider * sysref_div
            self.ui.VCODistFreq.setText(f"{vco_dist_freq:.5f}")
            logger.debug(f"VCODistFreq简化计算 (SYSREF Divider模式): {vco_dist_freq:.5f} MHz (PFD: {pll2_pfd_freq}, N: {n_divider}, SysrefDiv: {sysref_div})")
            self._notify_vco_dist_freq_changed(vco_dist_freq)
        else:
            self.ui.VCODistFreq.setText("0.00")
            logger.warning(f"VCODistFreq计算参数无效 (SYSREF Divider模式): PFD={pll2_pfd_freq}, N={n_divider}, SysrefDiv={sysref_div}")
            self._notify_vco_dist_freq_changed(0.0)
    else:
        # 其他Feedback模式: VCODistFreq = PLL2Cin × PLL2NDivider
        # ... 原有逻辑保持不变
```

## 测试验证

### 测试案例
通过测试验证了修复的正确性：

**测试案例1：**
- PLL2PFDFreq: 100.0 MHz
- PLL2NDivider: 50  
- SysrefDiv: 4000
- 计算结果: 100.0 × 50 × 4000 = 20,000,000 MHz (20 GHz)

**测试案例2：**
- PLL2PFDFreq: 122.88 MHz
- PLL2NDivider: 24
- SysrefDiv: 1
- 计算结果: 122.88 × 24 × 1 = 2949.12 MHz (2.95 GHz) ✅ 合理

**测试案例3：**
- PLL2PFDFreq: 100.0 MHz
- PLL2NDivider: 30
- SysrefDiv: 8
- 计算结果: 100.0 × 30 × 8 = 24,000 MHz (24 GHz)

## 关键改进

### 1. 正确的公式实现
- **修复前**: `VCODistFreq = PLL2PFDFreq × PLL2NDivider × 1.0` (错误)
- **修复后**: `VCODistFreq = PLL2PFDFreq × PLL2NDivider × SysrefDiv` (正确)

### 2. 跨窗口数据获取
- 正确从同步系统参考窗口获取SYSREF分频器值
- 添加了完善的错误处理和默认值机制

### 3. 详细的调试日志
- 添加了详细的计算过程日志
- 便于调试和验证计算结果

### 4. 双重修复
- 同时修复了主要计算方法和简化计算方法
- 确保所有代码路径都使用正确的公式

## 使用效果

### 修复前的问题
- FBMUX选择SYSREF Divider时，VCODistFreq计算错误
- 忽略了SYSREF分频器的影响
- 导致频率计算结果不准确

### 修复后的改进
- **正确计算**: 使用正确的公式计算VCODistFreq
- **跨窗口同步**: 正确获取SYSREF分频器值
- **完善日志**: 详细记录计算过程，便于调试
- **错误处理**: 添加了完善的异常处理机制

## 总结

通过这次修复，解决了FBMUX选择SYSREF Divider时VCODistFreq计算错误的问题。现在系统能够：

1. **正确识别**FBMUX=SYSREF Divider的情况
2. **准确获取**SYSREF分频器值
3. **正确计算**VCODistFreq = PLL2PFDFreq × PLL2NDivider × SysrefDiv
4. **提供详细**的计算过程日志

这确保了当用户选择SYSREF Divider作为反馈源时，系统能够提供准确的VCO分布频率计算结果。
