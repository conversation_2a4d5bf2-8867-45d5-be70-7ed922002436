{".class": "MypyFile", "_fullname": "consolidate_duplicate_docs", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DocumentConsolidator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "consolidate_duplicate_docs.DocumentConsolidator", "name": "DocumentConsolidator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "consolidate_duplicate_docs.DocumentConsolidator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "consolidate_duplicate_docs", "mro": ["consolidate_duplicate_docs.DocumentConsolidator", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "docs_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "consolidate_duplicate_docs.DocumentConsolidator.__init__", "name": "__init__", "type": null}}, "_create_merged_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "new_filename", "plan", "main_content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "consolidate_duplicate_docs.DocumentConsolidator._create_merged_content", "name": "_create_merged_content", "type": null}}, "backup_dir": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "consolidate_duplicate_docs.DocumentConsolidator.backup_dir", "name": "backup_dir", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "consolidate_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "consolidate_duplicate_docs.DocumentConsolidator.consolidate_all", "name": "consolidate_all", "type": null}}, "consolidation_plan": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "consolidate_duplicate_docs.DocumentConsolidator.consolidation_plan", "name": "consolidation_plan", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "create_backup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "consolidate_duplicate_docs.DocumentConsolidator.create_backup", "name": "create_backup", "type": null}}, "docs_dir": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "consolidate_duplicate_docs.DocumentConsolidator.docs_dir", "name": "docs_dir", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "merge_documents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "new_filename", "plan"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "consolidate_duplicate_docs.DocumentConsolidator.merge_documents", "name": "merge_documents", "type": null}}, "remove_original_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "plan"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "consolidate_duplicate_docs.DocumentConsolidator.remove_original_files", "name": "remove_original_files", "type": null}}, "update_category_readme": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "target_dir", "new_filename", "plan"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "consolidate_duplicate_docs.DocumentConsolidator.update_category_readme", "name": "update_category_readme", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "consolidate_duplicate_docs.DocumentConsolidator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "consolidate_duplicate_docs.DocumentConsolidator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "consolidate_duplicate_docs.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "consolidate_duplicate_docs.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "consolidate_duplicate_docs.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "consolidate_duplicate_docs.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "consolidate_duplicate_docs.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "consolidate_duplicate_docs.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "consolidate_duplicate_docs.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "consolidate_duplicate_docs.main", "name": "main", "type": null}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef"}, "success": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "consolidate_duplicate_docs.success", "name": "success", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "path": "E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\docs\\consolidate_duplicate_docs.py"}