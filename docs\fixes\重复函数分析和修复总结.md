# 重复函数分析和修复总结

## 发现的重复函数

### 1. ModernPLLHandler.py中的重复函数

#### 问题：PLL2计算函数重复
- **重复函数1**: `_calculate_pll2_output_with_source(input_freq)`
- **重复函数2**: `_calculate_pll2_output(oscin_freq)`

#### 功能分析：
- `_calculate_pll2_output_with_source`: 使用统一公式，调用`_calculate_pll2_unified_formula`
- `_calculate_pll2_output`: 使用简单的PFD计算公式 (OSCin × Doubler / R_Divider)

#### 使用情况：
- `_calculate_pll2_output_with_source`: 被3处调用，是主要的计算方法
- `_calculate_pll2_output`: 被2处调用，功能较简单

#### 修复方案：
✅ **保留**: `_calculate_pll2_output_with_source` (功能更完整)
❌ **移除**: `_calculate_pll2_output` (功能重复且简单)
🔄 **更新**: 将调用`_calculate_pll2_output`的地方改为调用`_calculate_pll2_output_with_source`

### 2. RegisterUpdateBus.py中的重复函数

#### 问题：SYSREF频率缓存函数重复
- **重复函数1**: `cache_sysref_freq(freq_value)` - 第一个版本
- **重复函数2**: `cache_sysref_freq(freq_value)` - 第二个版本
- **重复函数1**: `get_cached_sysref_freq()` - 第一个版本
- **重复函数2**: `get_cached_sysref_freq()` - 第二个版本

#### 功能分析：
- **第一个版本**: 只缓存值，记录info级别日志
- **第二个版本**: 缓存值 + 发送信号，记录debug级别日志

#### 修复方案：
✅ **保留**: 第一个版本，并合并第二个版本的信号发送功能
❌ **移除**: 第二个版本的重复函数

### 3. ModernSyncSysRefHandler.py中的缺失函数

#### 问题：缺少必要的辅助函数
- **缺失函数**: `_get_main_window()`

#### 影响：
- 代码中调用了`self._get_main_window()`但函数不存在
- 导致获取PLL2PFD频率时出错

#### 修复方案：
✅ **添加**: `_get_main_window()`方法，支持通过parent链和QApplication查找主窗口

## 实施的修复

### 1. ModernPLLHandler.py修复

#### 移除重复函数：
```python
# ❌ 移除了这个重复函数
def _calculate_pll2_output(self, oscin_freq):
    """计算PLL2的PFD频率"""
    # ... 简单的PFD计算逻辑
```

#### 更新函数调用：
```python
# 🔄 修改前
pll2_pfd_freq = self._calculate_pll2_output(pll2_input_freq)

# ✅ 修改后
pll2_pfd_freq = self._calculate_pll2_output_with_source(pll2_input_freq)
```

### 2. RegisterUpdateBus.py修复

#### 合并重复函数：
```python
# ✅ 保留并改进的版本
def cache_sysref_freq(self, freq_value):
    """缓存SYSREF频率值（专门用于PLL2Cin）"""
    try:
        self._frequency_cache['sysref_freq'] = freq_value
        logger.info(f"RegisterUpdateBus: 已缓存SYSREF频率值(用于PLL2Cin): {freq_value} MHz")
        
        # 发送SYSREF频率更新信号 ✅ 合并的功能
        if hasattr(self, 'sysref_freq_updated'):
            self.sysref_freq_updated.emit(freq_value)
    except Exception as e:
        logger.error(f"RegisterUpdateBus: 缓存SYSREF频率值时出错: {str(e)}")
```

#### 移除重复函数：
```python
# ❌ 移除了重复的函数定义
# def cache_sysref_freq(self, freq_value): ...
# def get_cached_sysref_freq(self): ...
```

### 3. ModernSyncSysRefHandler.py修复

#### 添加缺失函数：
```python
# ✅ 新增的必要函数
def _get_main_window(self):
    """获取主窗口实例"""
    try:
        # 通过parent链向上查找主窗口
        widget = self.ui
        while widget:
            if hasattr(widget, 'parent') and widget.parent():
                parent = widget.parent()
                if hasattr(parent, 'pll_window') or hasattr(parent, 'sync_sysref_window'):
                    return parent
                widget = parent
            else:
                break
        
        # 通过QApplication获取
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if app:
            for widget in app.topLevelWidgets():
                if hasattr(widget, 'pll_window') or hasattr(widget, 'sync_sysref_window'):
                    return widget
        
        return None
    except Exception as e:
        logger.error(f"获取主窗口时出错: {str(e)}")
        return None
```

## 修复效果

### 1. 代码质量提升
- ✅ **消除重复**: 移除了功能重复的函数
- ✅ **统一接口**: 使用统一的计算方法
- ✅ **完善功能**: 补充了缺失的必要函数

### 2. 功能完整性
- ✅ **PLL2计算**: 统一使用更完整的计算公式
- ✅ **SYSREF缓存**: 合并了缓存和信号发送功能
- ✅ **窗口访问**: 支持跨窗口的数据获取

### 3. 维护性改善
- ✅ **减少冗余**: 降低了代码维护成本
- ✅ **逻辑清晰**: 每个函数职责更明确
- ✅ **错误处理**: 改善了异常处理机制

## 验证要点

### 1. PLL2计算验证
- 确认PLL2PFD频率计算正确
- 验证VCODistFreq计算正确
- 检查PLL2Cin显示正确

### 2. SYSREF缓存验证
- 确认SYSREF频率正确缓存
- 验证信号正确发送
- 检查跨窗口数据同步

### 3. 窗口访问验证
- 确认能正确获取主窗口
- 验证能访问PLL窗口数据
- 检查错误处理机制

## 总结

通过仔细分析和修复重复函数：

1. **移除了2个重复的PLL2计算函数**
2. **合并了4个重复的SYSREF缓存函数**
3. **添加了1个缺失的窗口访问函数**

这些修复提升了代码质量，消除了功能冗余，完善了缺失功能，使整个系统更加稳定和易于维护。

### 关键改进：
- 🎯 **统一计算逻辑**: PLL2计算使用统一的复杂公式
- 🔄 **完善缓存机制**: SYSREF缓存支持信号发送
- 🔗 **改善窗口通信**: 支持可靠的跨窗口数据访问

现在代码结构更清晰，功能更完整，维护更容易！
