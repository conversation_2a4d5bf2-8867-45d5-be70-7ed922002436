{"data_mtime": 1750155115, "dep_lines": [368, 676, 15, 13, 14, 16, 10, 11, 12, 677, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [20, 20, 5, 5, 5, 5, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["core.services.version.VersionService", "core.services.register.RegisterManager", "ui.handlers.ModernBaseHandler", "PyQt5.QtWidgets", "PyQt5.QtCore", "utils.Log", "os", "sys", "traceback", "json", "builtins", "PyQt5", "PyQt5.QtGui", "PyQt5.sip", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "core", "core.services", "core.services.register", "core.services.version", "genericpath", "io", "json.decoder", "ntpath", "posixpath", "types", "typing", "utils"], "hash": "87239474c91c7d51ec530b12c7ca4acf7f33e65c", "id": "ui.handlers.ModernUIEventHandler", "ignore_all": false, "interface_hash": "e97dcb3a69da9d5155191570a231589d55e598f5", "mtime": 1749554579, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\ui\\handlers\\ModernUIEventHandler.py", "plugin_data": null, "size": 28361, "suppressed": [], "version_id": "1.15.0"}