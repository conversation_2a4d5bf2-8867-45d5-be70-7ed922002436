# PLL2简化计算方法说明

## 概述

基于用户反馈，我们实现了PLL2NDivider的简化计算方法。现在有了PLL2NDivider左侧的输入值（PLL2Cin），可以直接使用这个值来简化VCODistFreq的计算，避免了复杂的反馈公式平衡和自动调整机制。

## 实现的改进

### 1. 简化的计算公式

**原来的复杂方法：**
- 需要验证公式平衡性：`OSCin × Doubler / PLL2RDivider = PLL2NDivider输入值 × PLL2NDivider`
- 自动调整PLL2NDivider值以实现公式平衡
- 复杂的反馈路径计算和验证

**现在的简化方法：**
- **Prescaler模式** (PLL2NclkMux=0): `VCODistFreq = PLL2PFDFreq × PLL2NDivider × PLL2Prescaler`
- **Feedback模式** (PLL2NclkMux=1): `VCODistFreq = PLL2Cin × PLL2NDivider`

### 2. 核心代码修改

#### 主要修改的方法：

1. **`_calculate_pll2_unified_formula()`** - 简化了主计算流程
2. **`_update_vco_dist_freq_simplified()`** - 新增的简化VCODistFreq计算方法
3. **`_get_pll2_cin_frequency()`** - 获取PLL2Cin控件显示的频率值

#### 关键实现：

```python
def _update_vco_dist_freq_simplified(self, pll2_pfd_freq):
    """使用简化方法更新VCODistFreq值"""
    pll2_nclk_mux = self._get_pll2_nclk_mux_value()
    n_divider = self._get_pll2_n_divider_value()
    
    if pll2_nclk_mux == 0:
        # Prescaler模式
        prescaler_val = self._get_pll2_prescaler_value()
        vco_dist_freq = pll2_pfd_freq * n_divider * prescaler_val
    elif pll2_nclk_mux == 1:
        # Feedback模式 - 直接使用PLL2Cin值
        pll2_cin_freq = self._get_pll2_cin_frequency()
        vco_dist_freq = pll2_cin_freq * n_divider
```

## 优势分析

### 1. 计算逻辑更简单直观
- 不再需要复杂的公式平衡验证
- 直接基于显示的输入值进行计算
- 减少了计算步骤和潜在的错误点

### 2. 用户控制更灵活
- 用户可以自由修改PLL2NDivider值
- 不会被自动调整机制覆盖
- 更符合用户的直观操作习惯

### 3. 避免复杂的反馈调整
- 移除了自动PLL2NDivider调整逻辑
- 不再进行反馈公式平衡检查
- 简化了代码维护和调试

### 4. 基于实际显示值计算
- 直接使用PLL2Cin控件显示的值
- 确保计算与用户看到的值一致
- 提高了计算的透明度

## 测试验证

### Prescaler模式测试
```
测试参数:
  OSCin: 100.0 MHz
  PLL2NDivider: 50
  PLL2Prescaler: 2.0
  
计算结果:
  PLL2PFDFreq: 100.000 MHz
  VCODistFreq: 10000.00000 MHz (100.000 × 50 × 2.0)
```

### Feedback模式测试
```
测试参数:
  OSCin: 100.0 MHz
  PLL2NDivider: 40
  PLL2Cin: 125.0 MHz
  
计算结果:
  PLL2PFDFreq: 100.000 MHz
  VCODistFreq: 5000.00000 MHz (125.0 × 40)
```

## 使用场景

### 适用情况：
- 用户已经通过PLL2Cin了解了输入值
- 希望直接控制VCODistFreq的计算
- 不需要自动平衡调整功能
- 追求简单直观的计算逻辑

### 技术要求：
- PLL2Cin控件必须正确显示输入频率值
- PLL2NclkMux设置必须正确反映当前模式
- PLL2NDivider值由用户手动设置

## 向后兼容性

- 保留了原有的验证方法，但注释掉了自动调整逻辑
- 所有现有的控件和接口保持不变
- 可以通过配置开关在简化模式和复杂模式之间切换（如需要）

## 总结

这个简化的计算方法成功地解决了用户提出的问题：
1. ✅ 利用了PLL2Cin显示的输入值
2. ✅ 简化了VCODistFreq的计算逻辑
3. ✅ 移除了复杂的反馈公式平衡机制
4. ✅ 提高了用户操作的直观性和灵活性

通过这个改进，用户现在可以更直观地控制PLL2的计算过程，同时享受更简单、更可预测的计算结果。
