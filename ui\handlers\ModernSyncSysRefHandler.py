#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
现代化的同步系统参考处理器
使用ModernBaseHandler作为基类，重构自原SyncSysRefHandler
主要功能：同步系统参考配置、频率计算、批量控制
"""

from PyQt5 import QtCore, QtWidgets
from ui.handlers.ModernBaseHandler import ModernBaseHandler
from ui.forms.Ui_syncSysref import Ui_sync_sysref
from utils.Log import get_module_logger

logger = get_module_logger(__name__)


class ModernSyncSysRefHandler(ModernBaseHandler):
    """现代化的同步系统参考处理器"""

    # 添加窗口关闭信号
    window_closed = QtCore.pyqtSignal()

    def __init__(self, parent=None, register_manager=None, **kwargs):
        """初始化现代化同步系统参考处理器

        Args:
            parent: 父窗口
            register_manager: RegisterManager实例
            **kwargs: 其他参数（如register_repo等，用于兼容性）
        """
        super().__init__(parent, register_manager, **kwargs)

        # 设置窗口标题
        self.setWindowTitle("同步系统参考配置 (现代化版本)")

        # 创建UI实例
        self.ui = Ui_sync_sysref()
        self.ui.setupUi(self.content_widget)

        # 初始化同步系统特定配置
        self._init_sync_config()

        logger.info("现代化同步系统参考处理器初始化完成")

    def _init_sync_config(self):
        """初始化同步系统特定配置"""
        try:
            # 设置默认VCO频率
            self._init_default_values()

            # 连接特殊信号
            self._connect_special_signals()

            # 连接批量控制按钮
            self._connect_batch_control_buttons()

            logger.info("同步系统特定配置初始化完成")

        except Exception as e:
            logger.error(f"初始化同步系统配置时出错: {str(e)}")

    def _init_default_values(self):
        """初始化默认值"""
        try:
            # 设置默认VCO频率
            if hasattr(self.ui, "InternalVCOFreq"):
                self.ui.InternalVCOFreq.setText("2949.12")

            # 设置SYSREF分频器的正确范围和从RegisterManager获取当前值
            if hasattr(self.ui, "spinBoxSysrefDIV"):
                # 根据register.json，SYSREF_DIV[12:0]的范围是0:8191
                self.ui.spinBoxSysrefDIV.setMinimum(0)
                self.ui.spinBoxSysrefDIV.setMaximum(8191)

                # 从RegisterManager获取当前值，而不是使用JSON默认值
                current_sysref_div = self._get_sysref_div_from_register_manager()
                self.ui.spinBoxSysrefDIV.setValue(current_sysref_div)
                logger.info(f"已设置SYSREF分频器范围(0-8191)和当前值({current_sysref_div})，来源：RegisterManager")

                # 立即缓存SYSREF分频器值，供PLL窗口使用
                try:
                    from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
                    bus = RegisterUpdateBus.instance()
                    if bus and hasattr(bus, 'cache_sysref_data'):
                        bus.cache_sysref_data(None, current_sysref_div)
                        logger.info(f"【同步系统参考窗口】已缓存SYSREF分频器值: {current_sysref_div}")
                except Exception as e:
                    logger.warning(f"缓存SYSREF分频器值失败: {str(e)}")

            # 设置SYSREF延迟的正确范围和默认值
            if hasattr(self.ui, "spinBoxSysrefDDLY"):
                # 根据register.json，SYSREF_DDLY[12:0]的范围是2:8191
                self.ui.spinBoxSysrefDDLY.setMinimum(2)
                self.ui.spinBoxSysrefDDLY.setMaximum(8191)

                # 默认值是0000000001000（二进制）= 8（十进制）
                self.ui.spinBoxSysrefDDLY.setValue(8)
                logger.info("已设置SYSREF延迟范围(2-8191)和默认值(8)")

            # 设置DDLYd步进计数器的范围和默认值
            if hasattr(self.ui, "DDLYdStepCNT_1"):
                # 根据register.json，DDLYd_STEP_CNT[7:0]的范围是0:255
                self.ui.DDLYdStepCNT_1.setMinimum(0)
                self.ui.DDLYdStepCNT_1.setMaximum(255)
                self.ui.DDLYdStepCNT_1.setValue(0)  # 默认值是00000000
                logger.info("已设置DDLYd步进计数器范围(0-255)和默认值(0)")

            # 初始化ComboBox控件
            self._init_combobox_controls()

            # 初始化频率显示控件
            self._init_frequency_displays()

            # 初始化电源管理和控制状态
            self._init_power_and_control_states()

            # 计算并显示初始频率
            self.calculate_output_frequencies()

        except Exception as e:
            logger.error(f"初始化默认值时出错: {str(e)}")

    def _connect_special_signals(self):
        """连接特殊信号"""
        try:
            # 连接VCO频率输入信号
            if hasattr(self.ui, "InternalVCOFreq"):
                # 连接returnPressed信号（用户按回车时）
                self.ui.InternalVCOFreq.returnPressed.connect(self._on_internal_vco_freq_changed)
                # 连接textChanged信号（用户输入时实时响应）
                self.ui.InternalVCOFreq.textChanged.connect(self._on_internal_vco_freq_changed)
                logger.info("已连接 InternalVCOFreq returnPressed 和 textChanged 信号")

            # 连接分频器值变化信号
            if hasattr(self.ui, "spinBoxSysrefDIV"):
                self.ui.spinBoxSysrefDIV.valueChanged.connect(self._on_sysref_div_changed)
                logger.info("已连接 spinBoxSysrefDIV 信号")
            if hasattr(self.ui, "SendPulse"):
                self.ui.SendPulse.clicked.connect(self.send_pulse)
                logger.info("已连接 SendPulse 按钮信号")

            # 连接VCODistFreq更新信号
            self._connect_vco_dist_freq_signal()

            # 从缓存初始化VCODistFreq值
            self._initialize_vco_dist_freq_from_cache()

            # 初始化时从缓存加载SYSREF数据
            self._init_sysref_data_from_cache()

            # 初始化时根据PLL2PFD计算InternalVCOFreq
            self.calculate_internal_vco_freq_from_pll2pfd()

        except Exception as e:
            logger.error(f"连接特殊信号时出错: {str(e)}")

    def _connect_vco_dist_freq_signal(self):
        """连接VCODistFreq更新信号"""
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()

            # 连接VCODistFreq更新信号
            if hasattr(bus, 'vco_dist_freq_updated'):
                bus.vco_dist_freq_updated.connect(self.on_vco_dist_freq_updated)
                logger.info("已连接VCODistFreq更新信号")
            else:
                logger.warning("RegisterUpdateBus中缺少vco_dist_freq_updated信号")

        except Exception as e:
            logger.error(f"连接VCODistFreq更新信号时出错: {str(e)}")

    def _initialize_vco_dist_freq_from_cache(self):
        """从RegisterUpdateBus缓存初始化VCODistFreq值"""
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()

            # 尝试从缓存获取VCODistFreq值
            if hasattr(bus, 'get_cached_vco_dist_freq'):
                cached_vco_dist_freq = bus.get_cached_vco_dist_freq()

                if cached_vco_dist_freq is not None:
                    logger.info(f"【同步系统参考窗口】从缓存获取VCODistFreq: {cached_vco_dist_freq} MHz")

                    # 更新InternalVCOFreq控件
                    if hasattr(self.ui, "InternalVCOFreq"):
                        # 防止递归调用的标志
                        if hasattr(self, '_updating_internal_vco_freq') and self._updating_internal_vco_freq:
                            return

                        self._updating_internal_vco_freq = True
                        try:
                            # 格式化为5位小数的字符串
                            formatted_freq = f"{cached_vco_dist_freq:.5f}"
                            old_value = self.ui.InternalVCOFreq.text()

                            # 更新控件值
                            self.ui.InternalVCOFreq.setText(formatted_freq)
                            logger.info(f"【同步系统参考窗口】从缓存初始化InternalVCOFreq: '{old_value}' -> '{formatted_freq}'")

                            # 重新计算输出频率
                            self.calculate_output_frequencies()

                        finally:
                            self._updating_internal_vco_freq = False
                    else:
                        logger.warning("【同步系统参考窗口】InternalVCOFreq控件不存在")
                else:
                    logger.info("【同步系统参考窗口】缓存中没有VCODistFreq值，使用默认值")
            else:
                logger.warning("RegisterUpdateBus没有get_cached_vco_dist_freq方法")

        except Exception as e:
            logger.error(f"从缓存初始化VCODistFreq时出错: {str(e)}")

    def on_vco_dist_freq_updated(self, vco_dist_freq):
        """处理VCODistFreq更新事件

        VCODistFreq应该等于InternalVCOFreq，直接同步过去

        Args:
            vco_dist_freq: 从PLL窗口计算出的VCODistFreq值(MHz)
        """
        try:
            logger.info(f"【同步系统参考窗口】收到VCODistFreq更新: {vco_dist_freq} MHz")

            # VCODistFreq = InternalVCOFreq，直接同步
            if hasattr(self.ui, "InternalVCOFreq"):
                # 防止递归调用的标志
                if hasattr(self, '_updating_internal_vco_freq') and self._updating_internal_vco_freq:
                    return

                self._updating_internal_vco_freq = True
                try:
                    # 格式化为5位小数的字符串
                    formatted_freq = f"{vco_dist_freq:.5f}"
                    old_value = self.ui.InternalVCOFreq.text()

                    # 只有当值不同时才更新
                    if old_value != formatted_freq:
                        self.ui.InternalVCOFreq.setText(formatted_freq)
                        logger.info(f"【同步系统参考窗口】InternalVCOFreq同步更新: '{old_value}' -> '{formatted_freq}' (来自VCODistFreq)")

                        # 重新计算输出频率
                        self.calculate_output_frequencies()
                    else:
                        logger.debug("【同步系统参考窗口】InternalVCOFreq值未变化，跳过更新")

                finally:
                    self._updating_internal_vco_freq = False
            else:
                logger.warning("【同步系统参考窗口】InternalVCOFreq控件不存在")

        except Exception as e:
            logger.error(f"处理VCODistFreq更新时出错: {str(e)}")

    def _on_internal_vco_freq_changed(self):
        """处理InternalVCOFreq控件值变化"""
        try:
            # 如果正在更新InternalVCOFreq（来自VCODistFreq同步），则跳过
            if hasattr(self, '_updating_internal_vco_freq') and self._updating_internal_vco_freq:
                return

            # 用户手动修改了InternalVCOFreq，重新计算输出频率
            self.calculate_output_frequencies()

        except Exception as e:
            logger.error(f"处理InternalVCOFreq变化时出错: {str(e)}")

    def _init_combobox_controls(self):
        """初始化ComboBox控件的选项和默认值"""
        try:
            # 初始化SYNC模式ComboBox
            if hasattr(self.ui, "comboSyncMode"):
                self.ui.comboSyncMode.clear()
                self.ui.comboSyncMode.addItems(["0", "1", "2", "3"])
                self.ui.comboSyncMode.setCurrentIndex(1)  # 默认值是01
                logger.info("已初始化SYNC模式ComboBox")

            # 初始化SYSREF脉冲计数ComboBox
            if hasattr(self.ui, "comboSysrefPulseCnt"):
                self.ui.comboSysrefPulseCnt.clear()
                self.ui.comboSysrefPulseCnt.addItems(["0", "1", "2", "3"])
                self.ui.comboSysrefPulseCnt.setCurrentIndex(3)  # 默认值是11
                logger.info("已初始化SYSREF脉冲计数ComboBox")

            # 初始化SYSREF MUX ComboBox
            if hasattr(self.ui, "comboSysrefMux"):
                self.ui.comboSysrefMux.clear()
                self.ui.comboSysrefMux.addItems(["0: Normal", "1: Reclocked", "2: Pulser", "3: Continuous"])
                self.ui.comboSysrefMux.setCurrentIndex(0)  # 默认值是00
                logger.info("已初始化SYSREF MUX ComboBox")

            # 初始化DDLYd SYSREF步进ComboBox
            if hasattr(self.ui, "comboDDLYdSysrefStep"):
                self.ui.comboDDLYdSysrefStep.clear()
                for i in range(16):
                    self.ui.comboDDLYdSysrefStep.addItem(str(i))
                self.ui.comboDDLYdSysrefStep.setCurrentIndex(1)  # 默认值是0001
                logger.info("已初始化DDLYd SYSREF步进ComboBox")

            # 初始化CLKin0 Demux ComboBox
            if hasattr(self.ui, "CLKin0Demux"):
                self.ui.CLKin0Demux.clear()
                self.ui.CLKin0Demux.addItems(["Fin", "Feedback Mux (0-delay mode)", "PLL1", "Off"])
                self.ui.CLKin0Demux.setCurrentIndex(0)  # 默认选择CLKin0
                logger.info("已初始化CLKin0 Demux ComboBox")

            # 初始化VCO模式ComboBox
            if hasattr(self.ui, "comboVcoMode"):
                self.ui.comboVcoMode.clear()
                self.ui.comboVcoMode.addItems(["Internal", "External"])
                self.ui.comboVcoMode.setCurrentIndex(0)  # 默认选择Internal
                logger.info("已初始化VCO模式ComboBox")

            # 应用之前延迟设置的ComboBox值
            if hasattr(self, 'apply_pending_combobox_values'):
                self.apply_pending_combobox_values()

        except Exception as e:
            logger.error(f"初始化ComboBox控件时出错: {str(e)}")

    def _init_frequency_displays(self):
        """初始化频率显示控件"""
        try:
            # 初始化SYSREF频率显示控件
            frequency_controls = ["SyncSysrefFreq1", "SyncSysrefFreq2"]

            for control_name in frequency_controls:
                if hasattr(self.ui, control_name):
                    control = getattr(self.ui, control_name)
                    control.setText("0.96")  # 默认显示计算后的频率 2949.12/3072 ≈ 0.96
                    control.setReadOnly(True)  # 设置为只读
                    logger.info(f"已初始化{control_name}频率显示")

        except Exception as e:
            logger.error(f"初始化频率显示控件时出错: {str(e)}")

    def _init_power_and_control_states(self):
        """初始化电源管理和控制状态"""
        try:
            # 根据register.json的默认值设置关键控制位

            # SYNC_EN默认为1（启用）
            if hasattr(self.ui, "SyncEn"):
                self.ui.SyncEn.setChecked(True)
                logger.info("已设置SYNC_EN为启用状态")

            # SYSREF相关的电源控制位默认为1（关闭），需要根据实际需要调整
            power_down_controls = {
                "SysrefGBLPD": True,    # SYSREF_GBL_PD默认为1
                "sysrefPD": True,       # SYSREF_PD默认为1
                "sysrefDDLYPD": True,   # SYSREF_DDLY_PD默认为1
                "SysrefPlsrPd": True    # SYSREF_PLSR_PD默认为1
            }

            for control_name, default_state in power_down_controls.items():
                if hasattr(self.ui, control_name):
                    control = getattr(self.ui, control_name)
                    control.setChecked(default_state)
                    logger.info(f"已设置{control_name}为{'关闭' if default_state else '启用'}状态")

            # 设置其他重要的默认状态
            control_defaults = {
                "SyncPOL": False,       # SYNC_POL默认为0（正常）
                "Sync1SHOTEn": False,   # SYNC_1SHOT_EN默认为0（电平敏感）
                "SysrefCLR": False,     # SYSREF_CLR默认为0
                "SysrefReqEn": False,   # SYSREF_REQ_EN默认为0
                "sysrefDissysref": False, # SYNC_DISSYSREF默认为0
                "sysrefDDLYdEn": False  # DDLYd_SYSREF_EN默认为0
            }

            for control_name, default_state in control_defaults.items():
                if hasattr(self.ui, control_name):
                    control = getattr(self.ui, control_name)
                    control.setChecked(default_state)
                    logger.info(f"已设置{control_name}默认状态")

            # 设置SYNC DIS控件的默认状态（默认为0，即不禁用）
            sync_dis_controls = [
                "SYNCDIS12", "SYNCDIS10", "SYNCDIS8", "SYNCDIS6",
                "SYNCDIS4", "SYNCDIS2", "SYNCDIS0"
            ]

            for control_name in sync_dis_controls:
                if hasattr(self.ui, control_name):
                    control = getattr(self.ui, control_name)
                    control.setChecked(False)  # 默认不禁用同步
                    logger.debug(f"已设置{control_name}为不禁用状态")

            logger.info("电源管理和控制状态初始化完成")

        except Exception as e:
            logger.error(f"初始化电源管理和控制状态时出错: {str(e)}")

    def _connect_batch_control_buttons(self):
        """连接批量控制按钮"""
        try:
            # 连接All On按钮 - 启用所有SYNC DIS控件（禁用同步）
            if hasattr(self.ui, "pBtAllOn"):
                self.ui.pBtAllOn.clicked.connect(lambda: self.set_all_sync_dis(True))
                logger.info("已连接 pBtAllOn 按钮 - 点击将启用所有SYNC DIS控件（禁用同步）")

            # 连接All Off按钮 - 禁用所有SYNC DIS控件（启用同步）
            if hasattr(self.ui, "pBtAllOff"):
                self.ui.pBtAllOff.clicked.connect(lambda: self.set_all_sync_dis(False))
                logger.info("已连接 pBtAllOff 按钮 - 点击将禁用所有SYNC DIS控件（启用同步）")

        except Exception as e:
            logger.error(f"连接批量控制按钮时出错: {str(e)}")

    def on_register_value_changed(self, widget_name, reg_addr, reg_value):
        """处理寄存器值变化"""
        logger.info(f"同步系统: 寄存器 {reg_addr} 值变化 (控件: {widget_name}) -> 0x{reg_value:04X}")

        # 重新计算频率
        self.calculate_output_frequencies()

        # 处理特定控件的业务逻辑
        if widget_name.startswith("SYNCDIS"):
            self._handle_sync_dis_change(widget_name)
        elif widget_name in ["SYSREFMUX", "InternalVCO", "CLKDEMUX"]:
            self._handle_mux_change(widget_name)

    def on_global_register_update(self, reg_addr, reg_value):
        """处理全局寄存器更新"""
        logger.debug(f"同步系统: 收到全局更新 {reg_addr} = 0x{reg_value:04X}")

        # 重新计算频率
        self.calculate_output_frequencies()

    # === 业务逻辑方法 ===

    def calculate_output_frequencies(self):
        """计算同步系统参考频率"""
        try:
            # 获取VCO频率
            if not hasattr(self.ui, "InternalVCOFreq"):
                logger.warning("未找到InternalVCOFreq控件，无法计算频率")
                return

            fvco_text = self.ui.InternalVCOFreq.text()
            if not fvco_text:
                self._update_output_frequency_display(0.0)
                return

            # 转换为浮点数
            try:
                fvco = float(fvco_text)
                logger.info(f"【SYSREF计算】从InternalVCOFreq获取VCO频率: {fvco} MHz")
            except ValueError:
                logger.warning(f"无效的VCO频率输入: {fvco_text}")
                self._update_output_frequency_display(0.0)
                return

            # 获取分频比
            if not hasattr(self.ui, "spinBoxSysrefDIV"):
                logger.warning("未找到spinBoxSysrefDIV控件，无法计算频率")
                self._update_output_frequency_display(0.0)
                return

            sysref_div = self.ui.spinBoxSysrefDIV.value()
            logger.info(f"【SYSREF计算】SYSREF分频比: {sysref_div}")

            if sysref_div <= 0:
                logger.warning(f"无效的分频比: {sysref_div}")
                self._update_output_frequency_display(0.0)
                return

            # 计算SYSREF频率
            sysref_freq = fvco / sysref_div
            logger.info(f"【SYSREF计算】计算公式: {fvco} / {sysref_div} = {sysref_freq:.5f} MHz")

            # 检查计算结果是否合理
            if sysref_freq > 10000:  # 大于10GHz
                logger.error(f"【SYSREF计算】计算结果异常大: {sysref_freq:.5f} MHz，请检查VCO频率和分频比")
            elif sysref_freq < 0.001:  # 小于1KHz
                logger.error(f"【SYSREF计算】计算结果异常小: {sysref_freq:.5f} MHz，请检查VCO频率和分频比")
            else:
                logger.info(f"【SYSREF计算】计算完成，系统参考频率: {sysref_freq:.5f} MHz")

            # 更新SYSREF频率显示
            self._update_output_frequency_display(sysref_freq)

            # 缓存SYSREF数据到事件总线
            self._cache_sysref_data(sysref_freq, sysref_div)

        except Exception as e:
            logger.error(f"计算输出频率时发生错误: {str(e)}")

    def _cache_sysref_data(self, sysref_freq, sysref_div):
        """将SYSREF数据缓存到事件总线

        Args:
            sysref_freq (float): SYSREF频率值
            sysref_div (int): SYSREF分频器值
        """
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()
            if bus:
                bus.cache_sysref_data(sysref_freq, sysref_div)
                logger.info(f"【同步系统参考窗口】已缓存SYSREF数据 - 频率: {sysref_freq:.5f} MHz, 分频器: {sysref_div}")
            else:
                logger.warning("RegisterUpdateBus实例为None，无法缓存SYSREF数据")
        except Exception as e:
            logger.error(f"缓存SYSREF数据到事件总线时出错: {str(e)}")

    def _init_sysref_data_from_cache(self):
        """从缓存初始化SYSREF数据"""
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()

            # 尝试从缓存获取SYSREF数据
            if hasattr(bus, 'get_cached_sysref_freq') and hasattr(bus, 'get_cached_sysref_div'):
                cached_sysref_freq = bus.get_cached_sysref_freq()
                cached_sysref_div = bus.get_cached_sysref_div()

                # 如果缓存中有SYSREF分频器值，更新控件
                if cached_sysref_div is not None and hasattr(self.ui, "spinBoxSysrefDIV"):
                    self.ui.spinBoxSysrefDIV.setValue(cached_sysref_div)
                    logger.info(f"【同步系统参考窗口】从缓存初始化SYSREF分频器: {cached_sysref_div}")

                # 如果缓存中有SYSREF频率值，更新显示
                if cached_sysref_freq is not None:
                    self._update_output_frequency_display(cached_sysref_freq)
                    logger.info(f"【同步系统参考窗口】从缓存初始化SYSREF频率: {cached_sysref_freq:.5f} MHz")
                else:
                    logger.info("【同步系统参考窗口】缓存中没有SYSREF频率值")
            else:
                logger.warning("RegisterUpdateBus没有SYSREF缓存方法")

        except Exception as e:
            logger.error(f"从缓存初始化SYSREF数据时出错: {str(e)}")

    def calculate_internal_vco_freq_from_pll2pfd(self):
        """根据PLL2PFD、SYSREF分频器和PLL2NDivider计算InternalVCOFreq

        正确的计算公式: InternalVCOFreq = PLL2PFD × spinBoxSysrefDIV × PLL2NDivider
        """
        try:
            # 获取PLL2PFD频率（从PLL窗口或缓存）
            pll2_pfd_freq = self._get_pll2_pfd_frequency()
            if pll2_pfd_freq <= 0:
                logger.warning("【InternalVCOFreq计算】无法获取PLL2PFD频率")
                return

            # 获取SYSREF分频器值
            if not hasattr(self.ui, "spinBoxSysrefDIV"):
                logger.warning("【InternalVCOFreq计算】spinBoxSysrefDIV控件不存在")
                return

            sysref_div = self.ui.spinBoxSysrefDIV.value()
            if sysref_div <= 0:
                logger.warning(f"【InternalVCOFreq计算】无效的SYSREF分频器值: {sysref_div}")
                return

            # 获取PLL2NDivider值（从PLL窗口）
            pll2_n_divider = self._get_pll2_n_divider_value()
            if pll2_n_divider <= 0:
                logger.warning(f"【InternalVCOFreq计算】无法获取PLL2NDivider值: {pll2_n_divider}")
                return

            # 计算InternalVCOFreq = PLL2PFD × spinBoxSysrefDIV × PLL2NDivider
            internal_vco_freq = pll2_pfd_freq * sysref_div * pll2_n_divider
            logger.info(f"【InternalVCOFreq计算】计算公式: {pll2_pfd_freq} × {sysref_div} × {pll2_n_divider} = {internal_vco_freq:.5f} MHz")

            # 更新InternalVCOFreq控件
            if hasattr(self.ui, "InternalVCOFreq"):
                # 防止递归调用
                if hasattr(self, '_updating_internal_vco_freq') and self._updating_internal_vco_freq:
                    logger.debug("【InternalVCOFreq计算】正在更新中，跳过递归调用")
                    return

                self._updating_internal_vco_freq = True
                try:
                    formatted_freq = f"{internal_vco_freq:.5f}"
                    old_value = self.ui.InternalVCOFreq.text()

                    # 只有当值不同时才更新
                    if old_value != formatted_freq:
                        self.ui.InternalVCOFreq.setText(formatted_freq)
                        logger.info(f"【InternalVCOFreq计算】InternalVCOFreq更新: '{old_value}' -> '{formatted_freq}' MHz")

                        # 同步InternalVCOFreq到VCODistFreq（双向同步）
                        self._sync_internal_vco_to_vco_dist_freq(internal_vco_freq)

                        # 重新计算SYSREF输出频率
                        self.calculate_output_frequencies()
                    else:
                        logger.debug(f"【InternalVCOFreq计算】InternalVCOFreq值未变化: {formatted_freq}")

                finally:
                    self._updating_internal_vco_freq = False

        except Exception as e:
            logger.error(f"从PLL2PFD计算InternalVCOFreq时出错: {str(e)}")

    def _sync_internal_vco_to_vco_dist_freq(self, internal_vco_freq):
        """将InternalVCOFreq同步到PLL窗口的VCODistFreq

        Args:
            internal_vco_freq (float): InternalVCOFreq的值
        """
        try:
            logger.info(f"【双向同步】开始将InternalVCOFreq同步到VCODistFreq: {internal_vco_freq:.5f} MHz")

            # 通过事件总线通知PLL窗口更新VCODistFreq
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()

            if bus and hasattr(bus, 'notify_internal_vco_freq_changed'):
                bus.notify_internal_vco_freq_changed(internal_vco_freq)
                logger.info(f"【双向同步】✅ 已通知PLL窗口更新VCODistFreq: {internal_vco_freq:.5f} MHz")
            else:
                # 备用方案：直接访问PLL窗口
                main_window = self._get_main_window()
                if main_window and hasattr(main_window, 'pll_control_window'):
                    pll_window = main_window.pll_control_window
                    if pll_window and hasattr(pll_window.ui, 'VCODistFreq'):
                        formatted_freq = f"{internal_vco_freq:.5f}"
                        old_value = pll_window.ui.VCODistFreq.text()

                        if old_value != formatted_freq:
                            pll_window.ui.VCODistFreq.setText(formatted_freq)
                            logger.info(f"【双向同步】✅ 直接更新PLL窗口VCODistFreq: '{old_value}' -> '{formatted_freq}' MHz")

                            # ⚠️ 不要调用_notify_vco_dist_freq_changed，避免无限循环
                            # 只更新UI显示，不触发信号传递
                            logger.info("【双向同步】跳过信号通知，避免循环同步")
                        else:
                            logger.debug("【双向同步】VCODistFreq值未变化，跳过更新")
                    else:
                        logger.warning("【双向同步】PLL窗口或VCODistFreq控件不存在")
                else:
                    logger.warning("【双向同步】无法访问PLL窗口")

        except Exception as e:
            logger.error(f"同步InternalVCOFreq到VCODistFreq时出错: {str(e)}")

    def _get_pll2_pfd_frequency(self):
        """获取PLL2PFD频率值

        Returns:
            float: PLL2PFD频率值，如果无法获取则返回0
        """
        try:
            logger.info("【获取PLL2PFD】开始获取PLL2PFD频率...")

            # 尝试从事件总线缓存获取
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()
            if bus and hasattr(bus, 'get_cached_pll2_pfd_freq'):
                cached_freq = bus.get_cached_pll2_pfd_freq()
                logger.info(f"【获取PLL2PFD】缓存中的PLL2PFD频率: {cached_freq}")
                if cached_freq is not None:
                    logger.info(f"【获取PLL2PFD】✅ 从缓存获取PLL2PFD频率: {cached_freq} MHz")
                    return cached_freq
                else:
                    logger.info("【获取PLL2PFD】缓存中没有PLL2PFD频率")

            # 尝试从PLL窗口直接获取
            main_window = self._get_main_window()
            logger.info(f"【获取PLL2PFD】主窗口: {main_window is not None}")

            if main_window and hasattr(main_window, 'pll_control_window'):
                pll_window = main_window.pll_control_window
                logger.info(f"【获取PLL2PFD】PLL窗口: {pll_window is not None}")

                if pll_window and hasattr(pll_window.ui, 'PLL2PFDFreq'):
                    pfd_text = pll_window.ui.PLL2PFDFreq.text()
                    logger.info(f"【获取PLL2PFD】PLL2PFDFreq控件值: '{pfd_text}'")
                    if pfd_text and pfd_text.strip():
                        try:
                            pll2_pfd_freq = float(pfd_text)
                            logger.info(f"【获取PLL2PFD】✅ 从PLL窗口获取PLL2PFD频率: {pll2_pfd_freq} MHz")

                            # 将获取到的值缓存起来
                            if bus and hasattr(bus, 'cache_pll2_pfd_freq'):
                                bus.cache_pll2_pfd_freq(pll2_pfd_freq)
                                logger.info(f"【获取PLL2PFD】已将PLL2PFD频率缓存: {pll2_pfd_freq} MHz")

                            return pll2_pfd_freq
                        except ValueError:
                            logger.warning(f"【获取PLL2PFD】PLL2PFDFreq值无法转换为数字: '{pfd_text}'")
                    else:
                        logger.warning("【获取PLL2PFD】PLL2PFDFreq控件值为空")
                else:
                    logger.warning("【获取PLL2PFD】PLL窗口中未找到PLL2PFDFreq控件")
            else:
                logger.warning("【获取PLL2PFD】PLL窗口未打开或不存在")

            logger.warning("【获取PLL2PFD】❌ 无法获取PLL2PFD频率，返回0")
            return 0.0

        except Exception as e:
            logger.error(f"【获取PLL2PFD】获取PLL2PFD频率时出错: {str(e)}")
            return 0.0

    def _get_pll2_n_divider_value(self):
        """获取PLL2NDivider值

        Returns:
            float: PLL2NDivider值，如果无法获取则返回0
        """
        try:
            logger.info("【获取PLL2NDivider】开始获取PLL2NDivider值...")

            # 首先尝试从事件总线缓存获取
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()
            if bus and hasattr(bus, 'get_cached_pll2_n_divider'):
                cached_n_divider = bus.get_cached_pll2_n_divider()
                logger.info(f"【获取PLL2NDivider】缓存中的PLL2NDivider值: {cached_n_divider}")
                if cached_n_divider is not None:
                    logger.info(f"【获取PLL2NDivider】✅ 从缓存获取PLL2NDivider: {cached_n_divider}")
                    return cached_n_divider
                else:
                    logger.info("【获取PLL2NDivider】缓存中没有PLL2NDivider值")

            # 尝试从PLL窗口直接获取
            main_window = self._get_main_window()
            logger.info(f"【获取PLL2NDivider】主窗口: {main_window is not None}")

            # 检查PLL窗口的正确属性名（pll_control_window）
            if main_window and hasattr(main_window, 'pll_control_window'):
                pll_window = main_window.pll_control_window
                logger.info(f"【获取PLL2NDivider】PLL窗口: {pll_window is not None}")

                if pll_window and hasattr(pll_window.ui, 'PLL2NDivider'):
                    pll2_n_divider = pll_window.ui.PLL2NDivider.value()
                    logger.info(f"【获取PLL2NDivider】✅ 从PLL窗口获取PLL2NDivider: {pll2_n_divider}")

                    # 将获取到的值缓存起来
                    if bus and hasattr(bus, 'cache_pll2_n_divider'):
                        bus.cache_pll2_n_divider(pll2_n_divider)
                        logger.info(f"【获取PLL2NDivider】已将PLL2NDivider缓存: {pll2_n_divider}")

                    return pll2_n_divider
                else:
                    logger.warning("【获取PLL2NDivider】PLL窗口中未找到PLL2NDivider控件")
            else:
                logger.warning("【获取PLL2NDivider】PLL窗口未打开或不存在")

            # 如果无法从PLL窗口获取，使用默认值
            default_pll2_n_divider = 12  # 常见的默认值
            logger.warning(f"【获取PLL2NDivider】❌ 无法获取PLL2NDivider，使用默认值: {default_pll2_n_divider}")
            return default_pll2_n_divider

        except Exception as e:
            logger.error(f"【获取PLL2NDivider】获取PLL2NDivider时出错: {str(e)}")
            return 12  # 返回默认值

    def _get_main_window(self):
        """获取主窗口实例

        Returns:
            MainWindow: 主窗口实例，如果无法获取则返回None
        """
        try:
            # 通过parent链向上查找主窗口
            widget = self.ui
            while widget:
                if hasattr(widget, 'parent') and widget.parent():
                    parent = widget.parent()
                    # 检查是否是主窗口（通常主窗口会有特定的属性）
                    if hasattr(parent, 'pll_control_window') or hasattr(parent, 'sync_sysref_window'):
                        return parent
                    widget = parent
                else:
                    break

            # 如果通过parent链找不到，尝试通过QApplication获取
            from PyQt5.QtWidgets import QApplication
            app = QApplication.instance()
            if app:
                for widget in app.topLevelWidgets():
                    if hasattr(widget, 'pll_control_window') or hasattr(widget, 'sync_sysref_window'):
                        return widget

            return None

        except Exception as e:
            logger.error(f"获取主窗口时出错: {str(e)}")
            return None

    def _get_sysref_div_from_register_manager(self):
        """从RegisterManager获取SYSREF分频器的当前值

        Returns:
            int: SYSREF分频器的当前值，如果无法获取则返回默认值
        """
        try:
            logger.info("【SYSREF分频器初始化】从RegisterManager获取SYSREF_DIV当前值...")

            # 获取RegisterManager实例
            if hasattr(self, 'register_manager') and self.register_manager:
                # SYSREF_DIV在寄存器0x4A中，位字段为SYSREF_DIV[12:0]
                try:
                    # 尝试获取SYSREF_DIV位字段值
                    sysref_div_value = self.register_manager.get_bit_field_value("0x4A", "SYSREF_DIV[12:0]")
                    if sysref_div_value is not None:
                        logger.info(f"【SYSREF分频器初始化】✅ 从RegisterManager获取SYSREF_DIV: {sysref_div_value}")
                        return int(sysref_div_value)
                    else:
                        logger.warning("【SYSREF分频器初始化】RegisterManager中SYSREF_DIV位字段值为None")
                except Exception as e:
                    logger.warning(f"【SYSREF分频器初始化】从RegisterManager获取SYSREF_DIV失败: {str(e)}")

                # 备用方案：尝试直接从寄存器获取
                try:
                    reg_value = self.register_manager.get_register_value("0x4A")
                    if reg_value is not None:
                        # SYSREF_DIV[12:0]位于寄存器的低13位
                        sysref_div_value = reg_value & 0x1FFF  # 提取低13位
                        logger.info(f"【SYSREF分频器初始化】✅ 从寄存器0x4A提取SYSREF_DIV: {sysref_div_value}")
                        return int(sysref_div_value)
                except Exception as e:
                    logger.warning(f"【SYSREF分频器初始化】从寄存器0x4A获取值失败: {str(e)}")
            else:
                logger.warning("【SYSREF分频器初始化】RegisterManager不可用")

            # 如果无法从RegisterManager获取，使用合理的默认值
            default_value = 12  # 使用更合理的默认值，而不是3072
            logger.warning(f"【SYSREF分频器初始化】❌ 无法从RegisterManager获取SYSREF_DIV，使用默认值: {default_value}")
            return default_value

        except Exception as e:
            logger.error(f"【SYSREF分频器初始化】获取SYSREF分频器值时出错: {str(e)}")
            return 12  # 返回合理的默认值

    def _on_sysref_div_changed(self):
        """处理SYSREF分频器值变化

        当SYSREF分频器变化时：
        1. 重新计算InternalVCOFreq = PLL2PFD × spinBoxSysrefDIV
        2. 重新计算SYSREF输出频率
        """
        try:
            logger.info("【SYSREF分频器变化】SYSREF分频器值发生变化，重新计算InternalVCOFreq")

            # 重新计算InternalVCOFreq
            self.calculate_internal_vco_freq_from_pll2pfd()

        except Exception as e:
            logger.error(f"处理SYSREF分频器变化时出错: {str(e)}")

    def force_recalculate_internal_vco_freq(self):
        """强制重新计算InternalVCOFreq - 用于调试"""
        try:
            logger.info("【强制重新计算】开始强制重新计算InternalVCOFreq...")

            # 显示当前状态
            current_internal_vco = self.ui.InternalVCOFreq.text() if hasattr(self.ui, "InternalVCOFreq") else "N/A"
            current_sysref_div = self.ui.spinBoxSysrefDIV.value() if hasattr(self.ui, "spinBoxSysrefDIV") else "N/A"

            logger.info(f"【强制重新计算】当前InternalVCOFreq: {current_internal_vco}")
            logger.info(f"【强制重新计算】当前SYSREF分频器: {current_sysref_div}")

            # 强制重新计算
            self.calculate_internal_vco_freq_from_pll2pfd()

            # 显示计算后状态
            new_internal_vco = self.ui.InternalVCOFreq.text() if hasattr(self.ui, "InternalVCOFreq") else "N/A"
            logger.info(f"【强制重新计算】计算后InternalVCOFreq: {new_internal_vco}")

        except Exception as e:
            logger.error(f"强制重新计算InternalVCOFreq时出错: {str(e)}")

    def _notify_sysref_freq1_changed(self, sysref_freq):
        """通知其他窗口SyncSysrefFreq1值已更新，并缓存该值

        这个方法确保PLL窗口能够获取到最新的SYSREF频率值

        Args:
            sysref_freq (float): SYSREF频率值
        """
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus = RegisterUpdateBus.instance()

            # 缓存SyncSysrefFreq1的值，供PLL窗口的PLL2Cin使用
            if hasattr(bus, 'cache_sysref_freq'):
                bus.cache_sysref_freq(sysref_freq)
                logger.info(f"【同步系统参考窗口】已缓存SyncSysrefFreq1值: {sysref_freq:.5f} MHz，供PLL2Cin使用")

            # 发送SYSREF频率更新信号（如果需要实时通知）
            if hasattr(bus, 'sysref_freq_updated'):
                bus.sysref_freq_updated.emit(sysref_freq)
                logger.info(f"【同步系统参考窗口】已发送SyncSysrefFreq1更新信号: {sysref_freq:.5f} MHz")
            else:
                logger.debug("RegisterUpdateBus中没有sysref_freq_updated信号")

        except Exception as e:
            logger.error(f"通知SyncSysrefFreq1更新时发生错误: {str(e)}")

    def _update_output_frequency_display(self, frequency):
        """更新频率显示到所有输出频率控件

        Args:
            frequency: 计算得到的频率值
        """
        try:
            # 格式化频率显示（保留2位小数）
            freq_text = f"{frequency:.2f}"

            # 处理SysrefFreq控件
            if hasattr(self.ui, "SysrefFreq"):
                self.ui.SysrefFreq.setText(freq_text)
                logger.debug(f"SysrefFreq输出频率: {freq_text} MHz")

            # 处理SyncSysrefFreq1控件
            if hasattr(self.ui, "SyncSysrefFreq1"):
                old_value = self.ui.SyncSysrefFreq1.text()
                self.ui.SyncSysrefFreq1.setText(freq_text)
                logger.info(f"SyncSysrefFreq1输出频率更新: '{old_value}' -> '{freq_text}' MHz")

                # 发送SyncSysrefFreq1更新信号到事件总线，供PLL窗口使用
                self._notify_sysref_freq1_changed(frequency)

            # 处理SyncSysrefFreq2控件
            if hasattr(self.ui, "SyncSysrefFreq2"):
                self.ui.SyncSysrefFreq2.setText(freq_text)
                logger.debug(f"SyncSysrefFreq2输出频率: {freq_text} MHz")

            # 更新状态信息
            logger.info(f"SYSREF频率已更新: {freq_text} MHz")

        except Exception as e:
            logger.error(f"更新频率显示时出错: {str(e)}")

    def set_all_sync_dis(self, checked):
        """设置所有SYNC DIS复选框的状态并更新寄存器值

        Args:
            checked: 布尔值，True表示选中，False表示取消选中
        """
        try:
            # 获取所有SYNC DIS复选框
            sync_dis_checkboxes = []
            for attr_name in dir(self.ui):
                if attr_name.startswith('SYNCDIS') and hasattr(self.ui, attr_name):
                    attr_value = getattr(self.ui, attr_name)
                    if isinstance(attr_value, QtWidgets.QCheckBox):
                        sync_dis_checkboxes.append((attr_name, attr_value))

            if not sync_dis_checkboxes:
                logger.warning("未找到任何SYNCDIS复选框控件")
                return

            logger.info(f"找到 {len(sync_dis_checkboxes)} 个SYNCDIS复选框控件")

            # 设置批量操作标志，避免每个控件变化都触发单独的处理
            self._set_batch_operation(True)

            # 设置所有复选框的状态
            success_count = 0
            for widget_name, checkbox in sync_dis_checkboxes:
                try:
                    # 先更新UI控件状态
                    checkbox.setChecked(checked)
                    success_count += 1
                    logger.debug(f"已更新控件 {widget_name} 的UI状态为 {checked}")

                except Exception as e:
                    logger.error(f"更新控件 {widget_name} UI状态时发生错误: {str(e)}")

            # 清除批量操作标志
            self._set_batch_operation(False)

            # 批量更新所有相关寄存器
            self._batch_update_sync_dis_registers(checked)

            logger.info(f"已{('启用' if checked else '禁用')}所有SYNC DIS控件 ({success_count}/{len(sync_dis_checkboxes)})")

        except Exception as e:
            logger.error(f"设置所有SYNC DIS控件时发生错误: {str(e)}")

    def _batch_update_sync_dis_registers(self, checked):
        """批量更新所有SYNC DIS相关的寄存器"""
        try:
            if not self.register_manager:
                logger.warning("RegisterManager不可用，跳过寄存器更新")
                return

            updated_registers = set()
            success_count = 0

            # 遍历所有SYNC DIS控件并更新对应的寄存器位
            for widget_name in self.widget_register_map:
                if widget_name.startswith('SYNCDIS'):
                    try:
                        widget_info = self.widget_register_map[widget_name]
                        reg_addr = widget_info["register_addr"]
                        bit_def = widget_info["bit_def"]
                        bit_name = bit_def.get("name", "")

                        if bit_name:
                            # 更新寄存器位值
                            success = self.register_manager.set_bit_field_value(reg_addr, bit_name, 1 if checked else 0)
                            if success:
                                success_count += 1
                                updated_registers.add(reg_addr)
                                # 确保reg_addr是整数类型用于格式化
                                addr_int = int(reg_addr, 16) if isinstance(reg_addr, str) else reg_addr
                                logger.debug(f"已更新寄存器0x{addr_int:02X}中的{bit_name}位为{1 if checked else 0}")
                            else:
                                logger.warning(f"更新控件 {widget_name} 的寄存器值失败")

                    except Exception as e:
                        logger.error(f"更新控件 {widget_name} 寄存器时发生错误: {str(e)}")

            # 发送寄存器更新信号
            for reg_addr in updated_registers:
                try:
                    reg_value = self.register_manager.get_register_value(reg_addr)
                    self._send_register_update_signal(reg_addr, reg_value)

                    # 自动写入到芯片
                    self._auto_write_register_to_chip(reg_addr, reg_value)

                except Exception as e:
                    # 确保reg_addr是整数类型用于格式化
                    addr_int = int(reg_addr, 16) if isinstance(reg_addr, str) else reg_addr
                    logger.error(f"发送寄存器0x{addr_int:02X}更新信号时出错: {str(e)}")

            logger.info(f"批量更新完成: {success_count}个控件，{len(updated_registers)}个寄存器")

        except Exception as e:
            logger.error(f"批量更新SYNC DIS寄存器时出错: {str(e)}")

    def _send_register_update_signal(self, reg_addr, reg_value):
        """发送寄存器更新信号到全局总线"""
        try:
            from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
            bus_instance = RegisterUpdateBus.instance()
            if bus_instance:
                bus_instance.register_updated.emit(reg_addr, reg_value)
                # 确保reg_addr是整数类型用于格式化
                addr_int = int(reg_addr, 16) if isinstance(reg_addr, str) else reg_addr
                logger.debug(f"已发送寄存器0x{addr_int:02X}更新信号到全局总线")
            else:
                logger.warning("RegisterUpdateBus实例为None，跳过信号发送")
        except Exception as e:
            logger.warning(f"发送RegisterUpdateBus信号时出错: {str(e)}")

    def _set_batch_operation(self, value):
        """设置批量操作状态"""
        try:
            from core.services.BatchOperationState import BatchOperationState
            BatchOperationState.instance().set_batch_updating(value)
            logger.debug(f"批量操作状态设置为: {value}")
        except Exception as e:
            logger.warning(f"设置批量操作状态时出错: {str(e)}")

    def _handle_sync_dis_change(self, widget_name):
        """处理SYNC DIS控件变化"""
        try:
            logger.info(f"SYNC DIS控件 {widget_name} 状态变化")
            # 可以在这里添加特定的业务逻辑
        except Exception as e:
            logger.error(f"处理SYNC DIS控件变化时出错: {str(e)}")

    def _handle_mux_change(self, widget_name):
        """处理MUX控件变化"""
        try:
            logger.info(f"MUX控件 {widget_name} 状态变化")
            # 重新计算频率
            self.calculate_output_frequencies()
        except Exception as e:
            logger.error(f"处理MUX控件变化时出错: {str(e)}")

    # === 公共接口方法 ===

    def get_current_status(self):
        """获取当前同步系统状态

        Returns:
            dict: 当前状态信息
        """
        try:
            status = {}

            if self.register_manager:
                # 获取SYSREF配置状态
                status["sysref_enabled"] = True  # 默认启用

                # 获取SYNC DIS状态
                sync_dis_status = {}
                for widget_name in self.widget_register_map:
                    if widget_name.startswith("SYNCDIS"):
                        widget_info = self.widget_register_map[widget_name]
                        reg_addr = widget_info["register_addr"]
                        bit_def = widget_info["bit_def"]
                        bit_name = bit_def.get("name", "")

                        if bit_name:
                            bit_value = self.register_manager.get_bit_field_value(reg_addr, bit_name)
                            sync_dis_status[widget_name] = bool(bit_value)

                status["sync_dis_status"] = sync_dis_status

                # 获取频率信息
                if hasattr(self.ui, "InternalVCOFreq"):
                    try:
                        vco_freq = float(self.ui.InternalVCOFreq.text() or "0")
                        status["vco_frequency"] = vco_freq
                    except ValueError:
                        status["vco_frequency"] = 0.0

                if hasattr(self.ui, "spinBoxSysrefDIV"):
                    status["sysref_divider"] = self.ui.spinBoxSysrefDIV.value()

            return status

        except Exception as e:
            logger.error(f"获取同步系统状态时出错: {str(e)}")
            return {}

    def set_sync_preset(self, preset_name):
        """设置同步系统预设配置

        Args:
            preset_name: 预设名称
        """
        try:
            logger.info(f"应用同步系统预设: {preset_name}")

            # 定义预设配置
            presets = {
                "default": {
                    "vco_frequency": 2949.12,
                    "sysref_divider": 32,
                    "all_sync_dis": False
                },
                "high_frequency": {
                    "vco_frequency": 3000.0,
                    "sysref_divider": 16,
                    "all_sync_dis": False
                },
                "low_frequency": {
                    "vco_frequency": 2400.0,
                    "sysref_divider": 64,
                    "all_sync_dis": False
                },
                "all_disabled": {
                    "vco_frequency": 2949.12,
                    "sysref_divider": 32,
                    "all_sync_dis": True
                }
            }

            if preset_name not in presets:
                logger.warning(f"未知的预设: {preset_name}")
                return

            preset = presets[preset_name]

            # 应用预设
            if hasattr(self.ui, "InternalVCOFreq"):
                self.ui.InternalVCOFreq.setText(str(preset["vco_frequency"]))

            if hasattr(self.ui, "spinBoxSysrefDIV"):
                self.ui.spinBoxSysrefDIV.setValue(preset["sysref_divider"])

            # 设置所有SYNC DIS状态
            self.set_all_sync_dis(preset["all_sync_dis"])

            # 重新计算频率
            self.calculate_output_frequencies()

            logger.info(f"同步系统预设 {preset_name} 应用完成")

        except Exception as e:
            logger.error(f"设置同步系统预设时出错: {str(e)}")

    def get_sync_dis_count(self):
        """获取SYNC DIS控件的数量和状态

        Returns:
            dict: 包含总数和启用数的字典
        """
        try:
            total_count = 0
            enabled_count = 0

            for widget_name in self.widget_register_map:
                if widget_name.startswith("SYNCDIS"):
                    total_count += 1

                    widget_info = self.widget_register_map[widget_name]
                    reg_addr = widget_info["register_addr"]
                    bit_def = widget_info["bit_def"]
                    bit_name = bit_def.get("name", "")

                    if bit_name and self.register_manager:
                        bit_value = self.register_manager.get_bit_field_value(reg_addr, bit_name)
                        if bit_value:
                            enabled_count += 1

            return {
                "total_count": total_count,
                "enabled_count": enabled_count,
                "disabled_count": total_count - enabled_count
            }

        except Exception as e:
            logger.error(f"获取SYNC DIS状态时出错: {str(e)}")
            return {"total_count": 0, "enabled_count": 0, "disabled_count": 0}

    def send_pulse(self):
        """发送脉冲 - 对寄存器0x4E的bit7写入1"""
        try:
            reg_addr = 0x4E  # 寄存器地址
            
            logger.info(f"SendPulse按钮被点击，准备对寄存器0x{reg_addr:02X}的bit7写入1")
            
            # 检查RegisterManager是否可用
            if not self.register_manager:
                logger.error("RegisterManager不可用，无法执行脉冲发送")
                return
            
            # 获取当前寄存器值
            current_value = self.register_manager.get_register_value(reg_addr)
            logger.debug(f"当前寄存器0x{reg_addr:02X}的值: 0x{current_value:04X}")
            
            # 设置bit7为1 (bit7的掩码是0x80)
            new_value = current_value | 0x80
            logger.info(f"设置bit7后的新值: 0x{new_value:04X}")
            
            # 写入寄存器值到RegisterManager
            success = self.register_manager.set_register_value(reg_addr, new_value)
            
            if success:
                logger.info(f"成功更新寄存器0x{reg_addr:02X}的值为0x{new_value:04X}")
                
                # 发送寄存器更新信号到全局总线
                try:
                    from core.event_bus.RegisterUpdateBus import RegisterUpdateBus
                    bus_instance = RegisterUpdateBus.instance()
                    if bus_instance:
                        bus_instance.register_updated.emit(reg_addr, new_value)
                        logger.debug("已发送寄存器更新信号到全局总线")
                    else:
                        logger.warning("RegisterUpdateBus实例为None，跳过信号发送")
                except Exception as e:
                    logger.warning(f"发送RegisterUpdateBus信号时出错: {str(e)}")
                
                # 自动写入到芯片
                self._auto_write_register_to_chip(reg_addr, new_value)
                
                # 触发寄存器表格跳转
                if not self._is_in_batch_operation():
                    logger.info(f"SendPulse触发寄存器表格跳转到0x{reg_addr:02X}")
                    self._trigger_register_table_navigation(reg_addr)
                
                logger.info("脉冲发送操作完成")
            else:
                logger.error(f"更新寄存器0x{reg_addr:02X}失败")
                
        except Exception as e:
            logger.error(f"发送脉冲时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    @classmethod
    def create_for_testing(cls, parent=None):
        """创建测试实例"""
        try:
            # 创建模拟的RegisterManager
            from core.services.register.RegisterManager import RegisterManager
            import json
            import os
            
            # 加载寄存器配置
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'lib', 'register.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                registers_config = json.load(f)
            
            register_manager = RegisterManager(registers_config)
            
            # 创建实例
            instance = cls(parent, register_manager)
            
            logger.info("创建现代化SyncSysRefHandler测试实例成功")
            return instance
            
        except Exception as e:
            logger.error(f"创建测试实例时出错: {str(e)}")
            raise


if __name__ == "__main__":
    import sys
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # 创建测试实例
    handler = ModernSyncSysRefHandler.create_for_testing()
    handler.show()
    
    sys.exit(app.exec_())
