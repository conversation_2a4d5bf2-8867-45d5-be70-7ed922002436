{"data_mtime": 1754296716, "dep_lines": [9, 10, 11, 12, 13, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["os", "re", "pathlib", "collections", "logging", "builtins", "_collections_abc", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "typing"], "hash": "3d95ece10f45ec4559a2c5a6f03dc9527a4b921a", "id": "analyze_document_duplicates", "ignore_all": false, "interface_hash": "8002678939e2256df5ebd397154451a9b83fd6aa", "mtime": 1754296715, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\docs\\analyze_document_duplicates.py", "plugin_data": null, "size": 13976, "suppressed": [], "version_id": "1.15.0"}