{"data_mtime": 1750155115, "dep_lines": [12, 9, 10, 11, 41, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["utils.Log", "os", "json", "pathlib", "sys", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "io", "json.decoder", "types", "typing", "utils"], "hash": "50d61bc03d57e65b9ff12f75257151856438154c", "id": "core.services.version.VersionService", "ignore_all": false, "interface_hash": "c95e895a2a8666d3b1895d44b88648671e02c2b4", "mtime": 1751433008, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\FSJ04832\\FSJReadOutput\\version3\\anotherCore3\\core\\services\\version\\VersionService.py", "plugin_data": null, "size": 8901, "suppressed": [], "version_id": "1.15.0"}